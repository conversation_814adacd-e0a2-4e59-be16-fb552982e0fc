# 🤖 AI 驱动的 Sub-Agents 系统

## 🎯 概述

全新的 AI 驱动 Sub-Agents 系统完全消除了硬编码问题，使用智能提示词和真实的 AI 模型来完成需求分析、架构设计、代码实现等任务。

## ✨ 核心特性

### 🚀 AI 驱动分析
- **智能提示词系统**: 使用专业的提示词模板替代硬编码逻辑
- **多 AI 提供商支持**: OpenAI、Anthropic、本地模型
- **自动降级机制**: AI 不可用时自动使用基础分析
- **质量评估**: AI 生成内容的智能质量评分

### 🔧 配置驱动
- **统一配置管理**: 所有设置都可通过配置文件调整
- **环境特定配置**: 支持开发、测试、生产环境
- **运行时更新**: 配置变更无需重启
- **配置验证**: 自动验证配置的有效性

### 🎨 智能分析引擎
- **需求分析引擎**: AI 驱动的用户故事和验收标准生成
- **架构分析引擎**: 智能技术栈选择和系统设计
- **代码分析引擎**: 智能代码生成和质量评估
- **测试分析引擎**: 自动测试用例生成和覆盖率分析

## 🛠️ 配置指南

### 1. 基础配置

复制示例配置文件：
```bash
cp .vibecode/sub-agents-config.example.json .vibecode/sub-agents-config.json
```

### 2. AI 提供商配置

#### OpenAI 配置
```json
{
  "integrations": {
    "ai": {
      "provider": "openai",
      "model": "gpt-4",
      "apiKey": "sk-your-openai-api-key",
      "baseUrl": "https://api.openai.com/v1",
      "timeout": 30000,
      "maxTokens": 4000,
      "temperature": 0.7
    }
  }
}
```

#### Anthropic 配置
```json
{
  "integrations": {
    "ai": {
      "provider": "anthropic",
      "model": "claude-3-5-sonnet-20241022",
      "apiKey": "your-anthropic-api-key",
      "baseUrl": "https://api.anthropic.com/v1",
      "timeout": 30000,
      "maxTokens": 4000,
      "temperature": 0.7
    }
  }
}
```

#### 本地模型配置 (Ollama)
```json
{
  "integrations": {
    "ai": {
      "provider": "local",
      "model": "llama2",
      "baseUrl": "http://localhost:11434",
      "timeout": 60000,
      "maxTokens": 4000,
      "temperature": 0.7
    }
  }
}
```

### 3. 代理配置

每个代理都可以独立配置：
```json
{
  "agents": {
    "spec": {
      "enabled": true,
      "timeout": 45000,
      "maxRetries": 3,
      "qualityThreshold": 90,
      "capabilities": [
        "requirements-analysis",
        "user-story-generation",
        "acceptance-criteria-definition"
      ]
    }
  }
}
```

## 🚀 使用方法

### 基础用法
```bash
# 使用默认配置
/vibe-coding "开发用户认证系统"

# 指定质量阈值
/vibe-coding "开发电商平台" --qualityThreshold 90

# 启用特定代理
/vibe-coding "开发 CMS 系统" --enabledAgents spec,architect,developer

# 指定输出格式
/vibe-coding "开发 API 服务" --outputFormat json
```

### 高级用法
```bash
# 查看配置状态
/vibe-status

# 查看代理能力
/vibe-agents

# 检查 AI 服务健康状态
/vibe-health
```

## 🎨 AI 提示词系统

### 提示词模板结构

每个分析类型都有专门的提示词模板：

#### 需求分析提示词
```typescript
const requirementsPrompt = {
  system: `You are an expert business analyst...`,
  user: `Analyze this project and generate comprehensive requirements:
    Project Title: {title}
    Description: {description}
    Domain Context: {domain}
    ...`
};
```

#### 架构设计提示词
```typescript
const architecturePrompt = {
  system: `You are a senior software architect...`,
  user: `Design a comprehensive system architecture:
    Project Title: {title}
    Requirements: {requirements}
    Technology Preference: {technology}
    ...`
};
```

### 自定义提示词

可以通过配置文件自定义提示词：
```json
{
  "integrations": {
    "ai": {
      "customPrompts": {
        "requirements": {
          "system": "Your custom system prompt...",
          "user": "Your custom user prompt with {variables}..."
        }
      }
    }
  }
}
```

## 🔍 质量保证

### 多层质量检查
1. **AI 生成质量**: 评估 AI 输出的完整性和准确性
2. **内容验证**: 检查生成内容是否符合要求
3. **一致性检查**: 确保不同阶段输出的一致性
4. **最佳实践**: 验证是否遵循行业最佳实践

### 质量门控
```json
{
  "workflow": {
    "phaseThresholds": {
      "requirements": 90,
      "architecture": 85,
      "implementation": 85,
      "quality": 75,
      "testing": 70
    }
  }
}
```

## 🛡️ 错误处理

### 自动降级机制
- AI 服务不可用时自动使用基础分析
- 网络超时时的重试机制
- 配置错误时的默认值回退

### 错误监控
```typescript
// 自动记录和报告错误
console.error('AI analysis failed:', error);
// 发送到监控系统
await errorReporting.report(error);
```

## 📊 性能优化

### 缓存机制
- AI 响应缓存
- 配置缓存
- 分析结果缓存

### 并行处理
```json
{
  "workflow": {
    "parallelExecution": true
  }
}
```

## 🔧 故障排除

### 常见问题

1. **AI API 密钥无效**
   ```bash
   Error: AI integration not configured
   ```
   解决：检查配置文件中的 API 密钥

2. **网络连接问题**
   ```bash
   Error: AI service not available
   ```
   解决：检查网络连接和 API 端点

3. **配置格式错误**
   ```bash
   Error: Invalid configuration
   ```
   解决：使用配置验证工具检查格式

### 调试模式
```bash
DEBUG=vibe-coding:* /vibe-coding "项目描述"
```

## 🚀 最佳实践

1. **明确的项目描述**: 提供详细、清晰的项目描述
2. **合适的质量阈值**: 根据项目重要性设置质量要求
3. **定期更新配置**: 保持配置文件与项目需求同步
4. **监控 AI 使用**: 跟踪 AI API 使用量和成本
5. **备份配置**: 定期备份重要的配置文件

## 📈 未来规划

- [ ] 支持更多 AI 提供商
- [ ] 自定义提示词编辑器
- [ ] 实时协作功能
- [ ] 高级分析报告
- [ ] 集成开发环境插件

---

通过这个 AI 驱动的系统，Sub-Agents 现在具备了真正的智能分析能力，完全消除了硬编码问题，为开发团队提供了更加灵活、强大的自动化开发工具。
