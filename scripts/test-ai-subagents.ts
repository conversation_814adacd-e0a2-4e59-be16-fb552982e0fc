#!/usr/bin/env tsx
/**
 * AI 驱动 Sub-Agents 系统测试脚本
 * 验证新的智能分析引擎和配置系统
 */

import { SubAgentsConfigManager } from '../src/config/sub-agents-config.js';
import { AIRequirementsAnalysisEngine, AIArchitectureAnalysisEngine } from '../src/agents/engines/ai-analysis-engine.js';
import { AIService } from '../src/agents/integrations/ai-provider.js';

async function testConfigManager() {
  console.log('🔧 Testing Configuration Manager...');
  
  try {
    const configManager = SubAgentsConfigManager.getInstance(process.cwd());
    const config = configManager.getConfig();
    
    console.log('✅ Configuration loaded successfully');
    console.log(`   Version: ${config.version}`);
    console.log(`   AI Provider: ${config.integrations?.ai?.provider || 'not configured'}`);
    console.log(`   Enabled Agents: ${Object.keys(config.agents).filter(agent => (config.agents as any)[agent].enabled).join(', ')}`);
    
    // 验证配置
    const validation = configManager.validateConfig();
    if (validation.valid) {
      console.log('✅ Configuration validation passed');
    } else {
      console.log('❌ Configuration validation failed:');
      validation.errors.forEach(error => console.log(`   - ${error}`));
    }
    
    return true;
  } catch (error) {
    console.error('❌ Configuration test failed:', error);
    return false;
  }
}

async function testAIService() {
  console.log('\n🤖 Testing AI Service...');
  
  try {
    const aiService = AIService.create(process.cwd());
    
    // 检查健康状态
    const isHealthy = await aiService.checkHealth();
    console.log(`   Health Status: ${isHealthy ? '✅ Healthy' : '❌ Unhealthy'}`);
    
    // 获取支持的模型
    const models = aiService.getSupportedModels();
    console.log(`   Supported Models: ${models.join(', ')}`);
    
    if (isHealthy) {
      // 测试简单的 AI 调用
      const response = await aiService.sendMessage([
        { role: 'user', content: 'Hello, please respond with "AI service is working"' }
      ]);
      
      console.log(`   Test Response: ${response.content.substring(0, 50)}...`);
      console.log(`   Tokens Used: ${response.usage?.totalTokens || 'unknown'}`);
    }
    
    return isHealthy;
  } catch (error) {
    console.error('❌ AI Service test failed:', error);
    return false;
  }
}

async function testRequirementsAnalysis() {
  console.log('\n📋 Testing Requirements Analysis Engine...');
  
  try {
    const engine = new AIRequirementsAnalysisEngine({
      projectRoot: process.cwd(),
      title: '用户认证系统',
      description: '开发一个安全的用户认证系统，支持注册、登录、密码重置等功能'
    });
    
    console.log('   Starting requirements analysis...');
    const result = await engine.analyze();
    
    console.log('✅ Requirements analysis completed');
    console.log(`   Quality Score: ${result.quality}%`);
    console.log(`   Confidence: ${result.confidence}%`);
    console.log(`   Analysis Time: ${result.metadata.analysisTime}ms`);
    console.log(`   Method: ${result.metadata.method}`);
    console.log(`   User Stories: ${result.data.userStories?.length || 0}`);
    console.log(`   Acceptance Criteria: ${result.data.acceptanceCriteria?.length || 0}`);
    
    return true;
  } catch (error) {
    console.error('❌ Requirements analysis test failed:', error);
    return false;
  }
}

async function testArchitectureAnalysis() {
  console.log('\n🏗️ Testing Architecture Analysis Engine...');
  
  try {
    const engine = new AIArchitectureAnalysisEngine({
      projectRoot: process.cwd(),
      title: '电商平台',
      description: '开发一个现代化的电商平台，支持商品管理、订单处理、支付集成',
      requirements: {
        userStories: [
          { title: '商品浏览', description: '用户可以浏览商品' },
          { title: '购物车管理', description: '用户可以管理购物车' }
        ]
      }
    });
    
    console.log('   Starting architecture analysis...');
    const result = await engine.analyze();
    
    console.log('✅ Architecture analysis completed');
    console.log(`   Quality Score: ${result.quality}%`);
    console.log(`   Confidence: ${result.confidence}%`);
    console.log(`   Analysis Time: ${result.metadata.analysisTime}ms`);
    console.log(`   Method: ${result.metadata.method}`);
    
    if (result.data.technicalStack) {
      console.log(`   Frontend: ${result.data.technicalStack.frontend?.join(', ') || 'none'}`);
      console.log(`   Backend: ${result.data.technicalStack.backend?.join(', ') || 'none'}`);
      console.log(`   Database: ${result.data.technicalStack.database?.join(', ') || 'none'}`);
    }
    
    if (result.data.apiDesign) {
      console.log(`   API Style: ${result.data.apiDesign.style || 'unknown'}`);
      console.log(`   Authentication: ${result.data.apiDesign.authentication || 'unknown'}`);
      console.log(`   Endpoints: ${result.data.apiDesign.endpoints?.length || 0}`);
    }
    
    return true;
  } catch (error) {
    console.error('❌ Architecture analysis test failed:', error);
    return false;
  }
}

async function testPerformance() {
  console.log('\n⚡ Testing Performance...');
  
  const testCases = [
    { title: '简单项目', description: '一个简单的 TODO 应用' },
    { title: '中等项目', description: '一个博客系统，支持文章发布、评论、用户管理' },
    { title: '复杂项目', description: '一个企业级 ERP 系统，包含财务、人力资源、供应链管理等模块' }
  ];
  
  for (const testCase of testCases) {
    console.log(`   Testing: ${testCase.title}`);
    
    const startTime = Date.now();
    
    try {
      const engine = new AIRequirementsAnalysisEngine({
        projectRoot: process.cwd(),
        title: testCase.title,
        description: testCase.description
      });
      
      const result = await engine.analyze();
      const duration = Date.now() - startTime;
      
      console.log(`     ✅ Completed in ${duration}ms (Quality: ${result.quality}%)`);
    } catch (error) {
      const duration = Date.now() - startTime;
      console.log(`     ❌ Failed in ${duration}ms: ${error}`);
    }
  }
  
  return true;
}

async function generateTestReport() {
  console.log('\n📊 Generating Test Report...');
  
  const configManager = SubAgentsConfigManager.getInstance(process.cwd());
  const config = configManager.getConfig();
  
  const report = {
    timestamp: new Date().toISOString(),
    version: config.version,
    aiProvider: config.integrations?.ai?.provider || 'not configured',
    aiModel: config.integrations?.ai?.model || 'not configured',
    enabledAgents: Object.keys(config.agents).filter(agent => (config.agents as any)[agent].enabled),
    testResults: {
      configManager: true,
      aiService: false,
      requirementsAnalysis: false,
      architectureAnalysis: false,
      performance: false
    }
  };
  
  console.log('📋 Test Report:');
  console.log('='.repeat(50));
  console.log(`Timestamp: ${report.timestamp}`);
  console.log(`Version: ${report.version}`);
  console.log(`AI Provider: ${report.aiProvider}`);
  console.log(`AI Model: ${report.aiModel}`);
  console.log(`Enabled Agents: ${report.enabledAgents.join(', ')}`);
  console.log('\nTest Results:');
  Object.entries(report.testResults).forEach(([test, passed]) => {
    console.log(`  ${test}: ${passed ? '✅ PASSED' : '❌ FAILED'}`);
  });
  
  return report;
}

async function main() {
  console.log('🚀 Starting AI-Driven Sub-Agents System Tests\n');
  
  const results = {
    configManager: false,
    aiService: false,
    requirementsAnalysis: false,
    architectureAnalysis: false,
    performance: false
  };
  
  // 运行测试
  results.configManager = await testConfigManager();
  results.aiService = await testAIService();
  results.requirementsAnalysis = await testRequirementsAnalysis();
  results.architectureAnalysis = await testArchitectureAnalysis();
  results.performance = await testPerformance();
  
  // 生成报告
  await generateTestReport();
  
  // 总结
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log('\n🎯 Test Summary:');
  console.log(`   Passed: ${passedTests}/${totalTests}`);
  console.log(`   Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! AI-Driven Sub-Agents system is ready.');
  } else {
    console.log('⚠️  Some tests failed. Please check the configuration and AI service setup.');
  }
  
  process.exit(passedTests === totalTests ? 0 : 1);
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('💥 Test runner failed:', error);
    process.exit(1);
  });
}
