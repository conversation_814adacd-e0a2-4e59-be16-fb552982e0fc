/**
 * 测试简化配置系统
 * v1.4.0 - 简化配置革命
 */

import { loadSubAgentsConfig } from "./src/utils.js";

/**
 * 测试简化配置系统
 */
async function testSimplifiedConfig() {
  console.log('🧪 Testing Simplified Configuration System...\n');

  try {
    console.log('📖 Loading simplified configuration from .vibecode/config.json...');
    
    const config = await loadSubAgentsConfig('.');
    
    console.log('✅ Configuration loaded successfully!\n');
    
    console.log('⚙️ Simplified Sub-Agents Configuration:');
    console.log('=' .repeat(50));
    
    // 显示简化的配置
    console.log(`📊 Quality Settings:`);
    console.log(`   Overall Threshold: ${config.qualityThreshold}%`);
    console.log(`   Phase Thresholds:`);
    Object.entries(config.phaseThresholds).forEach(([phase, threshold]) => {
      console.log(`     • ${phase}: ${threshold}%`);
    });
    console.log('');
    
    console.log(`🤖 Agent Settings:`);
    console.log(`   Enabled Agents: ${config.enabledAgents.join(', ')}`);
    console.log(`   Max Retries per Phase: ${config.maxRetries}`);
    console.log(`   Max Workflow Retries: ${config.maxWorkflowRetries}`);
    console.log('');
    
    console.log(`🔄 Workflow Settings:`);
    console.log(`   Auto Restart on Quality Failure: ${config.autoRestart ? 'Yes' : 'No'}`);
    console.log(`   Show Progress: ${config.showProgress ? 'Yes' : 'No'}`);
    console.log(`   Save Results: ${config.saveResults ? 'Yes' : 'No'}`);
    console.log(`   Output Format: ${config.outputFormat}`);
    console.log('');
    
    // 验证配置的简洁性
    console.log('🔍 Configuration Analysis:');
    console.log('=' .repeat(50));
    
    const configKeys = Object.keys(config);
    console.log(`✅ Configuration Simplicity:`);
    console.log(`   Total Settings: ${configKeys.length} (vs 50+ in complex version)`);
    console.log(`   Nesting Levels: 1 (vs 4+ in complex version)`);
    console.log(`   User-friendly: High (simple key-value pairs)`);
    console.log(`   MCP-friendly: High (flat structure)`);
    console.log('');
    
    // 显示 MCP 工具友好性
    console.log('🛠️ MCP Tool Compatibility:');
    console.log('=' .repeat(50));
    console.log('✅ Flat structure - easy for tools to read');
    console.log('✅ Simple key-value pairs - no complex nesting');
    console.log('✅ Intuitive naming - self-explanatory keys');
    console.log('✅ Minimal configuration - only essential settings');
    console.log('✅ Default values - works out of the box');
    console.log('');
    
    // 显示用户习惯友好性
    console.log('👥 User Experience:');
    console.log('=' .repeat(50));
    console.log('✅ Familiar format - similar to package.json, tsconfig.json');
    console.log('✅ Easy to edit - no deep nesting to navigate');
    console.log('✅ Clear purpose - each setting has obvious meaning');
    console.log('✅ Quick setup - minimal configuration required');
    console.log('✅ Flexible - can override with command line args');
    console.log('');
    
    // 对比展示
    console.log('📊 Before vs After Comparison:');
    console.log('=' .repeat(50));
    
    console.log('❌ Before (Complex):');
    console.log('   • 200+ lines of configuration');
    console.log('   • 4+ levels of nesting');
    console.log('   • 50+ configuration options');
    console.log('   • Located in .vibecode/workflows/config.json');
    console.log('   • Overwhelming for new users');
    console.log('');
    
    console.log('✅ After (Simplified):');
    console.log(`   • ${JSON.stringify(config, null, 2).split('\n').length} lines of configuration`);
    console.log('   • 1 level of nesting');
    console.log(`   • ${configKeys.length} essential options`);
    console.log('   • Located in .vibecode/config.json');
    console.log('   • Intuitive for all users');
    console.log('');
    
    // 使用示例
    console.log('💡 Usage Examples:');
    console.log('=' .repeat(50));
    
    console.log('1. Default usage (no config needed):');
    console.log('   vibe-coding "开发用户认证系统" --rootPath ./project');
    console.log('');
    
    console.log('2. Quick quality adjustment:');
    console.log('   Edit .vibecode/config.json:');
    console.log('   "qualityThreshold": 98');
    console.log('');
    
    console.log('3. Enable/disable agents:');
    console.log('   Edit .vibecode/config.json:');
    console.log('   "enabledAgents": ["spec", "architect", "developer"]');
    console.log('');
    
    console.log('4. Override with command line:');
    console.log('   vibe-coding "开发系统" --qualityThreshold 90');
    console.log('');
    
    console.log('5. Show current config:');
    console.log('   show-config');
    console.log('');
    
    console.log('🎉 Simplified configuration test completed successfully!');
    
    // 验证配置有效性
    const validation = validateSimplifiedConfig(config);
    if (validation.isValid) {
      console.log('\n✅ Configuration is valid and ready to use!');
    } else {
      console.log('\n❌ Configuration issues found:');
      validation.errors.forEach(error => console.log(`   • ${error}`));
    }

  } catch (error) {
    console.error('❌ Simplified configuration test failed:', error);
    process.exit(1);
  }
}

/**
 * 验证简化配置的有效性
 */
function validateSimplifiedConfig(config: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // 验证质量阈值
  if (typeof config.qualityThreshold !== 'number' || 
      config.qualityThreshold < 0 || 
      config.qualityThreshold > 100) {
    errors.push('qualityThreshold must be a number between 0 and 100');
  }
  
  // 验证输出格式
  if (!['detailed', 'simple', 'json'].includes(config.outputFormat)) {
    errors.push('outputFormat must be one of: detailed, simple, json');
  }
  
  // 验证代理列表
  if (!Array.isArray(config.enabledAgents) || config.enabledAgents.length === 0) {
    errors.push('enabledAgents must be a non-empty array');
  }
  
  // 验证重试次数
  if (typeof config.maxRetries !== 'number' || config.maxRetries < 0) {
    errors.push('maxRetries must be a non-negative number');
  }
  
  // 验证阶段阈值
  if (typeof config.phaseThresholds !== 'object') {
    errors.push('phaseThresholds must be an object');
  } else {
    Object.entries(config.phaseThresholds).forEach(([phase, threshold]) => {
      if (typeof threshold !== 'number' || threshold < 0 || threshold > 100) {
        errors.push(`Phase threshold for ${phase} must be between 0 and 100`);
      }
    });
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 显示配置文件内容
 */
async function showConfigFile() {
  try {
    const fs = await import('fs/promises');
    const path = await import('path');
    
    const configPath = path.join('.', '.vibecode', 'config.json');
    const configContent = await fs.readFile(configPath, 'utf-8');
    
    console.log('\n📄 Current .vibecode/config.json content:');
    console.log('=' .repeat(50));
    console.log(configContent);
    
  } catch (error) {
    console.log('\n⚠️  No config file found - using defaults');
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  testSimplifiedConfig()
    .then(() => showConfigFile())
    .catch(console.error);
}

export { testSimplifiedConfig };
