/**
 * Vibe Coding Unified Workflow
 * Revolutionary Sub-Agents functionality like Claude Code
 * One command: /vibe-coding 开发用户认证系统
 * Complete automation: Spec → Code → Quality → Tests with 95% quality gates
 * v1.4.0 - Sub-Agents Revolution
 */

import { z } from "zod";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { ProfessionalWorkflow, ProfessionalWorkflowContext } from "../agents/workflows/professional-workflow.js";
import { SubAgentsConfigManager } from "../config/sub-agents-config.js";
import { analyzeCodebase } from "../analysis/engine.js";
import { performPredictionAnalysis } from "../prediction/engine.js";
import { CodeGenerationEngine } from "../generation/engine.js";
import { AgentType } from "../agents/types.js";

export interface UnifiedWorkflowResult {
  phase: string;
  success: boolean;
  qualityScore: number;
  output: any;
  executionTime: number;
  recommendations?: string[];
}

/**
 * Register Unified Vibe Coding Tool
 */
export function registerVibeCodingUnified(server: McpServer): void {
  console.log(`🚀 Registering Unified Vibe Coding Tool...`);

  // Main unified workflow tool - Revolutionary Sub-Agents functionality
  server.tool(
    "vibe-coding",
    "🚀 Revolutionary Sub-Agents workflow: One command to complete entire development cycle from requirements to code with 95% quality gates. Just like Claude Code!",
    {
      description: z.string().describe("Project description (e.g., '开发用户认证系统')"),
      rootPath: z.string().optional().describe("Project root path (default: current directory)"),
      qualityThreshold: z.number().min(80).max(100).default(95).describe("Quality threshold percentage"),
      enabledPhases: z.array(z.enum(["spec", "architecture", "implementation", "quality", "testing"])).optional().describe("Enabled phases (default: all)"),
      outputFormat: z.enum(["detailed", "summary", "json"]).default("detailed").describe("Output format"),
      autoExecute: z.boolean().default(true).describe("Auto-execute all phases without interruption")
    },
    async ({ description, rootPath = ".", qualityThreshold, enabledPhases, outputFormat, autoExecute }) => {
      const startTime = Date.now();
      console.log(`🚀 Starting Revolutionary Vibe Coding Workflow: ${description}`);
      console.log(`☕ Grab a coffee and watch the AI expert team work!`);

      try {
        // Phase 1: Initialize and Configure
        console.log(`\n📋 Phase 1: Initialization & Configuration`);
        const configManager = SubAgentsConfigManager.getInstance(rootPath);
        const config = configManager.getConfig();

        const finalQualityThreshold = qualityThreshold || 95;
        const finalEnabledPhases = enabledPhases || ["spec", "architecture", "implementation", "quality", "testing"];

        console.log(`🎯 Quality Threshold: ${finalQualityThreshold}%`);
        console.log(`🔄 Enabled Phases: ${finalEnabledPhases.join(' → ')}`);

        const results: UnifiedWorkflowResult[] = [];

        // Phase 2: Specification Generation (Spec Agent)
        if (finalEnabledPhases.includes("spec")) {
          console.log(`\n📋 Phase 2: Specification Generation (Requirements Analysis Expert)`);
          const specResult = await executeSpecificationPhase(description, rootPath, finalQualityThreshold);
          results.push(specResult);

          if (!specResult.success) {
            return formatFailureResult("Specification Generation", specResult, results);
          }
        }

        // Phase 3: Architecture Design (Architect Agent)
        if (finalEnabledPhases.includes("architecture")) {
          console.log(`\n🏗️ Phase 3: Architecture Design (Senior Software Architect)`);
          const archResult = await executeArchitecturePhase(description, rootPath, finalQualityThreshold, results);
          results.push(archResult);

          if (!archResult.success) {
            return formatFailureResult("Architecture Design", archResult, results);
          }
        }

        // Phase 4: Code Implementation (Developer Agent + Generation Engine)
        if (finalEnabledPhases.includes("implementation")) {
          console.log(`\n💻 Phase 4: Code Implementation (Senior Full-Stack Engineer)`);
          const implResult = await executeImplementationPhase(description, rootPath, finalQualityThreshold, results);
          results.push(implResult);

          if (!implResult.success) {
            return formatFailureResult("Code Implementation", implResult, results);
          }
        }

        // Phase 5: Quality Analysis (Quality Agent + Intelligence Engine)
        if (finalEnabledPhases.includes("quality")) {
          console.log(`\n🔍 Phase 5: Quality Analysis (Senior Quality Engineer)`);
          const qualityResult = await executeQualityPhase(description, rootPath, finalQualityThreshold, results);
          results.push(qualityResult);

          if (!qualityResult.success) {
            return formatFailureResult("Quality Analysis", qualityResult, results);
          }
        }

        // Phase 6: Test Generation (Test Agent + Generation Engine)
        if (finalEnabledPhases.includes("testing")) {
          console.log(`\n🧪 Phase 6: Test Generation (Senior Test Engineer)`);
          const testResult = await executeTestingPhase(description, rootPath, finalQualityThreshold, results);
          results.push(testResult);

          if (!testResult.success) {
            return formatFailureResult("Test Generation", testResult, results);
          }
        }

        // Final Summary
        const totalTime = Date.now() - startTime;
        const successRate = (results.filter(r => r.success).length / results.length) * 100;
        const avgQuality = results.reduce((sum, r) => sum + r.qualityScore, 0) / results.length;

        console.log(`\n🎉 Revolutionary Workflow Complete!`);
        console.log(`⏱️ Total Time: ${Math.round(totalTime / 1000)}s`);
        console.log(`📊 Success Rate: ${successRate.toFixed(1)}%`);
        console.log(`🎯 Average Quality: ${avgQuality.toFixed(1)}%`);

        return formatSuccessResult(description, results, totalTime, outputFormat);

      } catch (error) {
        console.error('❌ Revolutionary workflow failed:', error);
        return {
          content: [
            {
              type: "text",
              text: `❌ **Revolutionary Vibe Coding Workflow Failed**\n\nError: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );
}

/**
 * Execute Specification Phase
 */
async function executeSpecificationPhase(description: string, rootPath: string, qualityThreshold: number): Promise<UnifiedWorkflowResult> {
  const startTime = Date.now();

  try {
    // Use Professional Workflow for spec generation
    const workflowContext: ProfessionalWorkflowContext = {
      projectRoot: rootPath,
      projectTitle: extractTitle(description),
      projectDescription: description,
      qualityThresholds: {
        requirements: qualityThreshold,
        architecture: qualityThreshold,
        implementation: qualityThreshold,
        quality: qualityThreshold,
        testing: qualityThreshold
      },
      enabledAgents: ['spec'],
      outputFormat: 'detailed'
    };

    const workflow = new ProfessionalWorkflow(workflowContext);
    const results = await workflow.execute();
    const specResult = results.find(r => r.phase === 'requirements');

    if (specResult && specResult.success) {
      console.log(`✅ Specification generated with ${specResult.qualityScore}% quality`);
      return {
        phase: "Specification Generation",
        success: true,
        qualityScore: specResult.qualityScore,
        output: specResult.result,
        executionTime: Date.now() - startTime
      };
    } else {
      throw new Error("Specification generation failed quality gate");
    }

  } catch (error) {
    console.log(`❌ Specification generation failed: ${error}`);
    return {
      phase: "Specification Generation",
      success: false,
      qualityScore: 0,
      output: null,
      executionTime: Date.now() - startTime,
      recommendations: [`Fix specification generation: ${error instanceof Error ? error.message : String(error)}`]
    };
  }
}

/**
 * Execute Architecture Phase
 */
async function executeArchitecturePhase(description: string, rootPath: string, qualityThreshold: number, previousResults: UnifiedWorkflowResult[]): Promise<UnifiedWorkflowResult> {
  const startTime = Date.now();

  try {
    // Get specification from previous phase
    const specResult = previousResults.find(r => r.phase === "Specification Generation");

    // Use Professional Workflow for architecture design
    const workflowContext: ProfessionalWorkflowContext = {
      projectRoot: rootPath,
      projectTitle: extractTitle(description),
      projectDescription: description,
      qualityThresholds: {
        requirements: qualityThreshold,
        architecture: qualityThreshold,
        implementation: qualityThreshold,
        quality: qualityThreshold,
        testing: qualityThreshold
      },
      enabledAgents: ['architect'],
      outputFormat: 'detailed'
    };

    const workflow = new ProfessionalWorkflow(workflowContext);
    const results = await workflow.execute();
    const archResult = results.find(r => r.phase === 'architecture');

    if (archResult && archResult.success) {
      console.log(`✅ Architecture designed with ${archResult.qualityScore}% quality`);
      return {
        phase: "Architecture Design",
        success: true,
        qualityScore: archResult.qualityScore,
        output: archResult.result,
        executionTime: Date.now() - startTime
      };
    } else {
      throw new Error("Architecture design failed quality gate");
    }

  } catch (error) {
    console.log(`❌ Architecture design failed: ${error}`);
    return {
      phase: "Architecture Design",
      success: false,
      qualityScore: 0,
      output: null,
      executionTime: Date.now() - startTime,
      recommendations: [`Fix architecture design: ${error instanceof Error ? error.message : String(error)}`]
    };
  }
}

/**
 * Execute Implementation Phase
 */
async function executeImplementationPhase(description: string, rootPath: string, qualityThreshold: number, previousResults: UnifiedWorkflowResult[]): Promise<UnifiedWorkflowResult> {
  const startTime = Date.now();

  try {
    // Analyze codebase first
    const codebaseAnalysis = await analyzeCodebase(rootPath);
    const predictionAnalysis = await performPredictionAnalysis(codebaseAnalysis);

    // Create generation engine
    const generationEngine = new CodeGenerationEngine(codebaseAnalysis, predictionAnalysis);

    // Use Professional Workflow for implementation
    const workflowContext: ProfessionalWorkflowContext = {
      projectRoot: rootPath,
      projectTitle: extractTitle(description),
      projectDescription: description,
      qualityThresholds: {
        requirements: qualityThreshold,
        architecture: qualityThreshold,
        implementation: qualityThreshold,
        quality: qualityThreshold,
        testing: qualityThreshold
      },
      enabledAgents: ['developer'],
      outputFormat: 'detailed'
    };

    const workflow = new ProfessionalWorkflow(workflowContext);
    const results = await workflow.execute();
    const implResult = results.find(r => r.phase === 'implementation');

    if (implResult && implResult.success) {
      console.log(`✅ Code implemented with ${implResult.qualityScore}% quality`);
      return {
        phase: "Code Implementation",
        success: true,
        qualityScore: implResult.qualityScore,
        output: implResult.result,
        executionTime: Date.now() - startTime
      };
    } else {
      throw new Error("Code implementation failed quality gate");
    }

  } catch (error) {
    console.log(`❌ Code implementation failed: ${error}`);
    return {
      phase: "Code Implementation",
      success: false,
      qualityScore: 0,
      output: null,
      executionTime: Date.now() - startTime,
      recommendations: [`Fix code implementation: ${error instanceof Error ? error.message : String(error)}`]
    };
  }
}

/**
 * Execute Quality Analysis Phase
 */
async function executeQualityPhase(description: string, rootPath: string, qualityThreshold: number, previousResults: UnifiedWorkflowResult[]): Promise<UnifiedWorkflowResult> {
  const startTime = Date.now();

  try {
    // Analyze codebase for quality metrics
    const codebaseAnalysis = await analyzeCodebase(rootPath);
    const predictionAnalysis = await performPredictionAnalysis(codebaseAnalysis);

    // Use Professional Workflow for quality analysis
    const workflowContext: ProfessionalWorkflowContext = {
      projectRoot: rootPath,
      projectTitle: extractTitle(description),
      projectDescription: description,
      qualityThresholds: {
        requirements: qualityThreshold,
        architecture: qualityThreshold,
        implementation: qualityThreshold,
        quality: qualityThreshold,
        testing: qualityThreshold
      },
      enabledAgents: ['quality'],
      outputFormat: 'detailed'
    };

    const workflow = new ProfessionalWorkflow(workflowContext);
    const results = await workflow.execute();
    const qualityResult = results.find(r => r.phase === 'quality');

    if (qualityResult && qualityResult.success) {
      console.log(`✅ Quality analysis completed with ${qualityResult.qualityScore}% score`);
      return {
        phase: "Quality Analysis",
        success: true,
        qualityScore: qualityResult.qualityScore,
        output: {
          ...qualityResult.result,
          codebaseMetrics: codebaseAnalysis.metrics,
          securityAnalysis: predictionAnalysis.security,
          technicalDebt: predictionAnalysis.technicalDebt
        },
        executionTime: Date.now() - startTime
      };
    } else {
      throw new Error("Quality analysis failed quality gate");
    }

  } catch (error) {
    console.log(`❌ Quality analysis failed: ${error}`);
    return {
      phase: "Quality Analysis",
      success: false,
      qualityScore: 0,
      output: null,
      executionTime: Date.now() - startTime,
      recommendations: [`Fix quality analysis: ${error instanceof Error ? error.message : String(error)}`]
    };
  }
}

/**
 * Execute Testing Phase
 */
async function executeTestingPhase(description: string, rootPath: string, qualityThreshold: number, previousResults: UnifiedWorkflowResult[]): Promise<UnifiedWorkflowResult> {
  const startTime = Date.now();

  try {
    // Analyze codebase for test generation
    const codebaseAnalysis = await analyzeCodebase(rootPath);
    const predictionAnalysis = await performPredictionAnalysis(codebaseAnalysis);

    // Create generation engine for test generation
    const generationEngine = new CodeGenerationEngine(codebaseAnalysis, predictionAnalysis);

    // Use Professional Workflow for test strategy
    const workflowContext: ProfessionalWorkflowContext = {
      projectRoot: rootPath,
      projectTitle: extractTitle(description),
      projectDescription: description,
      qualityThresholds: {
        requirements: qualityThreshold,
        architecture: qualityThreshold,
        implementation: qualityThreshold,
        quality: qualityThreshold,
        testing: qualityThreshold
      },
      enabledAgents: ['test'],
      outputFormat: 'detailed'
    };

    const workflow = new ProfessionalWorkflow(workflowContext);
    const results = await workflow.execute();
    const testResult = results.find(r => r.phase === 'testing');

    if (testResult && testResult.success) {
      console.log(`✅ Test strategy completed with ${testResult.qualityScore}% coverage`);
      return {
        phase: "Test Generation",
        success: true,
        qualityScore: testResult.qualityScore,
        output: testResult.result,
        executionTime: Date.now() - startTime
      };
    } else {
      throw new Error("Test generation failed quality gate");
    }

  } catch (error) {
    console.log(`❌ Test generation failed: ${error}`);
    return {
      phase: "Test Generation",
      success: false,
      qualityScore: 0,
      output: null,
      executionTime: Date.now() - startTime,
      recommendations: [`Fix test generation: ${error instanceof Error ? error.message : String(error)}`]
    };
  }
}

/**
 * Extract title from description
 */
function extractTitle(description: string): string {
  const firstSentence = description.split(/[.!?]/)[0];
  return firstSentence.length > 50 ? firstSentence.substring(0, 50) + '...' : firstSentence;
}

/**
 * Format failure result
 */
function formatFailureResult(failedPhase: string, failedResult: UnifiedWorkflowResult, allResults: UnifiedWorkflowResult[]): any {
  const completedPhases = allResults.filter(r => r.success);

  return {
    content: [
      {
        type: "text",
        text: `❌ **Revolutionary Workflow Failed at ${failedPhase}**

## 📊 Progress Summary
- **Completed Phases**: ${completedPhases.length}/${allResults.length}
- **Failed Phase**: ${failedPhase}
- **Quality Score**: ${failedResult.qualityScore}%

## ✅ Completed Phases
${completedPhases.map(r => `- ${r.phase}: ${r.qualityScore}% (${Math.round(r.executionTime / 1000)}s)`).join('\n')}

## ❌ Failed Phase
- **${failedPhase}**: ${failedResult.qualityScore}% (${Math.round(failedResult.executionTime / 1000)}s)

## 💡 Recommendations
${failedResult.recommendations?.map(rec => `- ${rec}`).join('\n') || 'No specific recommendations available'}

## 🔄 Next Steps
1. Review the failed phase requirements
2. Check quality thresholds and adjust if needed
3. Re-run the workflow with: \`vibe-coding "${failedResult.phase}"\``,
      },
    ],
  };
}

/**
 * Format success result
 */
function formatSuccessResult(description: string, results: UnifiedWorkflowResult[], totalTime: number, outputFormat: string): any {
  const successRate = (results.filter(r => r.success).length / results.length) * 100;
  const avgQuality = results.reduce((sum, r) => sum + r.qualityScore, 0) / results.length;

  if (outputFormat === 'json') {
    return {
      content: [
        {
          type: "text",
          text: JSON.stringify({
            description,
            totalTime,
            successRate,
            avgQuality,
            results
          }, null, 2),
        },
      ],
    };
  }

  if (outputFormat === 'summary') {
    return {
      content: [
        {
          type: "text",
          text: `🎉 **Revolutionary Workflow Complete!**

**Project**: ${description}
**Success Rate**: ${successRate.toFixed(1)}%
**Average Quality**: ${avgQuality.toFixed(1)}%
**Total Time**: ${Math.round(totalTime / 1000)}s

${results.map(r => `${r.success ? '✅' : '❌'} ${r.phase}: ${r.qualityScore}%`).join('\n')}`,
        },
      ],
    };
  }

  // Detailed format
  const phaseDetails = results.map(result => `
## ${result.success ? '✅' : '❌'} ${result.phase}

**Quality Score**: ${result.qualityScore}%
**Execution Time**: ${Math.round(result.executionTime / 1000)}s
**Status**: ${result.success ? 'Passed Quality Gate' : 'Failed Quality Gate'}

${result.recommendations && result.recommendations.length > 0 ?
      `**Recommendations**:\n${result.recommendations.map(rec => `- ${rec}`).join('\n')}` :
      '**No issues found**'
    }
`).join('\n');

  return {
    content: [
      {
        type: "text",
        text: `# 🚀 Revolutionary Vibe Coding Workflow Results

## 📊 Executive Summary
- **Project**: ${description}
- **Success Rate**: ${successRate.toFixed(1)}%
- **Average Quality**: ${avgQuality.toFixed(1)}%
- **Total Execution Time**: ${Math.round(totalTime / 1000)}s
- **Quality Gates**: 95% threshold maintained

${phaseDetails}

## 🎉 Workflow Complete!

${successRate === 100 ?
            '🎯 **All phases completed successfully!** Your project is ready for deployment.' :
            '⚠️ **Some phases need attention.** Review the recommendations above.'
          }

## 🚀 What was accomplished:
1. **Specification Generation**: Professional requirements analysis with user stories
2. **Architecture Design**: Scalable system architecture with technology recommendations
3. **Code Implementation**: Production-ready code with best practices
4. **Quality Analysis**: Comprehensive quality assessment with security scanning
5. **Test Generation**: Complete test suite with high coverage

**Just like Claude Code - but even better!** ☕`,
      },
    ],
  };
}
