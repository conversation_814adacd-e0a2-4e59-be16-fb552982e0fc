/**
 * 智能代码生成工具集
 * v1.3.0 核心生成工具实现
 */

import { z } from "zod";
import { analyzeCodebase } from "../analysis/engine.js";
import { performPredictionAnalysis } from "../prediction/engine.js";
import { CodeGenerationEngine } from "../generation/engine.js";
import {
  CodeGenerationRequest,
  GenerationType,
  ProgrammingLanguage,
  GenerationOptions
} from "../generation/types.js";

/**
 * 注册代码生成工具
 */
export function registerGenerationTools(server: any) {
  console.log(`🚀 Registering code generation tools...`);

  // 1. 智能代码生成
  server.setRequestHandler(
    {
      method: "tools/call",
      params: {
        name: "generate-code",
        arguments: z.object({
          rootPath: z.string().describe("项目根路径"),
          description: z.string().describe("代码生成描述，详细说明要生成什么"),
          type: z.enum(["function", "class", "module", "component", "test", "refactor", "optimization"])
            .default("function")
            .describe("生成类型"),
          language: z.enum(["typescript", "javascript", "python", "java", "go", "rust"])
            .default("typescript")
            .describe("目标编程语言"),
          targetFile: z.string().optional().describe("目标文件路径（可选）"),
          outputFormat: z.enum(["code", "diff", "suggestion"])
            .default("code")
            .describe("输出格式"),
          includeComments: z.boolean().default(true).describe("包含注释"),
          includeTypes: z.boolean().default(true).describe("包含类型注解"),
          includeTests: z.boolean().default(false).describe("包含测试"),
          qualityLevel: z.enum(["basic", "standard", "premium"])
            .default("standard")
            .describe("质量级别"),
          stylePreference: z.enum(["conservative", "modern", "aggressive"])
            .default("modern")
            .describe("风格偏好")
        })
      }
    },
    async ({ rootPath, description, type, language, targetFile, outputFormat, includeComments, includeTypes, includeTests, qualityLevel, stylePreference }) => {
      try {
        console.log(`🚀 Generating ${type} code: ${description}`);

        // 1. 分析代码库
        const codebaseAnalysis = await analyzeCodebase(rootPath);

        // 2. 执行预测分析
        const predictionAnalysis = await performPredictionAnalysis(codebaseAnalysis);

        // 3. 创建生成引擎
        const generationEngine = new CodeGenerationEngine(codebaseAnalysis, predictionAnalysis);

        // 4. 构建生成请求
        const request: CodeGenerationRequest = {
          type: type as GenerationType,
          language: language as ProgrammingLanguage,
          description,
          context: {
            projectRoot: rootPath,
            targetFile,
            relatedFiles: [],
            styleGuide: {
              namingConventions: {
                functions: "camelCase",
                variables: "camelCase",
                classes: "PascalCase",
                files: "camelCase",
                constants: "UPPER_CASE"
              },
              formatting: {
                indentation: "spaces",
                indentSize: 2,
                lineLength: 100,
                semicolons: "always",
                quotes: "double"
              },
              architecturalConventions: {
                directoryStructure: [],
                importPatterns: [],
                errorHandlingPatterns: [],
                asyncPatterns: []
              },
              documentationConventions: {
                commentStyle: "jsdoc",
                documentationCoverage: "standard"
              }
            },
            architecturePatterns: [],
            dependencies: []
          },
          options: {
            outputFormat,
            includeComments,
            includeTypes,
            includeTests,
            qualityLevel,
            stylePreference
          } as GenerationOptions
        };

        // 5. 执行代码生成
        const result = await generationEngine.generateCode(request);

        // 6. 格式化输出
        if (outputFormat === "json") {
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(result, null, 2),
              },
            ],
          };
        }

        const report = formatCodeGenerationReport(result, type, description);

        return {
          content: [
            {
              type: "text",
              text: report,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ 代码生成失败: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // 2. 智能重构建议
  server.setRequestHandler(
    {
      method: "tools/call",
      params: {
        name: "suggest-refactor",
        arguments: z.object({
          rootPath: z.string().describe("项目根路径"),
          targetFile: z.string().optional().describe("目标文件路径（可选，不指定则分析整个项目）"),
          refactorType: z.enum(["extract_method", "extract_class", "rename", "move_method", "inline_method", "replace_conditional", "all"])
            .default("all")
            .describe("重构类型"),
          priority: z.enum(["low", "medium", "high", "all"])
            .default("all")
            .describe("优先级过滤"),
          outputFormat: z.enum(["detailed", "summary", "json"])
            .default("detailed")
            .describe("输出格式")
        })
      }
    },
    async ({ rootPath, targetFile, refactorType, priority, outputFormat }) => {
      try {
        console.log(`🔄 Generating refactor suggestions for ${targetFile || 'entire project'}...`);

        // 1. 分析代码库
        const codebaseAnalysis = await analyzeCodebase(rootPath);

        // 2. 执行预测分析
        const predictionAnalysis = await performPredictionAnalysis(codebaseAnalysis);

        // 3. 创建生成引擎
        const generationEngine = new CodeGenerationEngine(codebaseAnalysis, predictionAnalysis);

        // 4. 构建重构请求
        const request: CodeGenerationRequest = {
          type: "refactor",
          language: "typescript", // 默认，实际应该从项目检测
          description: `Generate ${refactorType} refactoring suggestions${targetFile ? ` for ${targetFile}` : ''}`,
          context: {
            projectRoot: rootPath,
            targetFile,
            relatedFiles: [],
            styleGuide: {
              namingConventions: {
                functions: "camelCase",
                variables: "camelCase",
                classes: "PascalCase",
                files: "camelCase",
                constants: "UPPER_CASE"
              },
              formatting: {
                indentation: "spaces",
                indentSize: 2,
                lineLength: 100,
                semicolons: "always",
                quotes: "double"
              },
              architecturalConventions: {
                directoryStructure: [],
                importPatterns: [],
                errorHandlingPatterns: [],
                asyncPatterns: []
              },
              documentationConventions: {
                commentStyle: "jsdoc",
                documentationCoverage: "standard"
              }
            },
            architecturePatterns: [],
            dependencies: []
          },
          options: {
            outputFormat: "suggestion",
            includeComments: true,
            includeTypes: true,
            includeTests: false,
            qualityLevel: "standard",
            stylePreference: "modern"
          }
        };

        // 5. 执行重构分析
        const result = await generationEngine.generateCode(request);

        // 6. 格式化输出
        if (outputFormat === "json") {
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(result, null, 2),
              },
            ],
          };
        }

        const report = formatRefactorSuggestionsReport(result, refactorType, priority, outputFormat === "detailed");

        return {
          content: [
            {
              type: "text",
              text: report,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ 重构建议生成失败: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // 3. 自动测试生成
  server.setRequestHandler(
    {
      method: "tools/call",
      params: {
        name: "generate-tests",
        arguments: z.object({
          rootPath: z.string().describe("项目根路径"),
          targetFile: z.string().describe("目标文件路径"),
          testTypes: z.array(z.enum(["unit", "integration", "e2e", "performance", "security", "accessibility"]))
            .default(["unit"])
            .describe("测试类型"),
          framework: z.enum(["jest", "mocha", "vitest", "jasmine", "cypress", "playwright"])
            .default("jest")
            .describe("测试框架"),
          coverageTarget: z.number().min(0).max(100).default(80).describe("覆盖率目标"),
          mockingStrategy: z.enum(["none", "minimal", "standard", "comprehensive"])
            .default("standard")
            .describe("模拟策略"),
          outputFormat: z.enum(["detailed", "summary", "json"])
            .default("detailed")
            .describe("输出格式")
        })
      }
    },
    async ({ rootPath, targetFile, testTypes, framework, coverageTarget, mockingStrategy, outputFormat }) => {
      try {
        console.log(`🧪 Generating ${testTypes.join(', ')} tests for ${targetFile}...`);

        // 1. 分析代码库
        const codebaseAnalysis = await analyzeCodebase(rootPath);

        // 2. 执行预测分析
        const predictionAnalysis = await performPredictionAnalysis(codebaseAnalysis);

        // 3. 创建生成引擎
        const generationEngine = new CodeGenerationEngine(codebaseAnalysis, predictionAnalysis);

        // 4. 构建测试生成请求
        const request: CodeGenerationRequest = {
          type: "test",
          language: "typescript", // 默认，实际应该从项目检测
          description: `Generate ${testTypes.join(', ')} tests for ${targetFile} using ${framework} framework`,
          context: {
            projectRoot: rootPath,
            targetFile,
            relatedFiles: [],
            styleGuide: {
              namingConventions: {
                functions: "camelCase",
                variables: "camelCase",
                classes: "PascalCase",
                files: "camelCase",
                constants: "UPPER_CASE"
              },
              formatting: {
                indentation: "spaces",
                indentSize: 2,
                lineLength: 100,
                semicolons: "always",
                quotes: "double"
              },
              architecturalConventions: {
                directoryStructure: [],
                importPatterns: [],
                errorHandlingPatterns: [],
                asyncPatterns: []
              },
              documentationConventions: {
                commentStyle: "jsdoc",
                documentationCoverage: "standard"
              }
            },
            architecturePatterns: [],
            dependencies: []
          },
          options: {
            outputFormat: "code",
            includeComments: true,
            includeTypes: true,
            includeTests: true,
            qualityLevel: "standard",
            stylePreference: "modern"
          }
        };

        // 5. 执行测试生成
        const result = await generationEngine.generateCode(request);

        // 6. 格式化输出
        if (outputFormat === "json") {
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(result, null, 2),
              },
            ],
          };
        }

        const report = formatTestGenerationReport(result, testTypes, framework, coverageTarget, outputFormat === "detailed");

        return {
          content: [
            {
              type: "text",
              text: report,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ 测试生成失败: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  // 4. 性能优化建议
  server.setRequestHandler(
    {
      method: "tools/call",
      params: {
        name: "optimize-performance",
        arguments: z.object({
          rootPath: z.string().describe("项目根路径"),
          targetFile: z.string().optional().describe("目标文件路径（可选）"),
          optimizationType: z.enum(["algorithm", "data_structure", "caching", "async", "memory", "io", "database", "network", "all"])
            .default("all")
            .describe("优化类型"),
          priority: z.enum(["low", "medium", "high", "all"])
            .default("all")
            .describe("优先级过滤"),
          outputFormat: z.enum(["detailed", "summary", "json"])
            .default("detailed")
            .describe("输出格式")
        })
      }
    },
    async ({ rootPath, targetFile, optimizationType, priority, outputFormat }) => {
      try {
        console.log(`⚡ Generating performance optimizations for ${targetFile || 'entire project'}...`);

        // 1. 分析代码库
        const codebaseAnalysis = await analyzeCodebase(rootPath);

        // 2. 执行预测分析
        const predictionAnalysis = await performPredictionAnalysis(codebaseAnalysis);

        // 3. 创建生成引擎
        const generationEngine = new CodeGenerationEngine(codebaseAnalysis, predictionAnalysis);

        // 4. 构建优化请求
        const request: CodeGenerationRequest = {
          type: "optimization",
          language: "typescript", // 默认，实际应该从项目检测
          description: `Generate ${optimizationType} performance optimizations${targetFile ? ` for ${targetFile}` : ''}`,
          context: {
            projectRoot: rootPath,
            targetFile,
            relatedFiles: [],
            styleGuide: {
              namingConventions: {
                functions: "camelCase",
                variables: "camelCase",
                classes: "PascalCase",
                files: "camelCase",
                constants: "UPPER_CASE"
              },
              formatting: {
                indentation: "spaces",
                indentSize: 2,
                lineLength: 100,
                semicolons: "always",
                quotes: "double"
              },
              architecturalConventions: {
                directoryStructure: [],
                importPatterns: [],
                errorHandlingPatterns: [],
                asyncPatterns: []
              },
              documentationConventions: {
                commentStyle: "jsdoc",
                documentationCoverage: "standard"
              }
            },
            architecturePatterns: [],
            dependencies: []
          },
          options: {
            outputFormat: "suggestion",
            includeComments: true,
            includeTypes: true,
            includeTests: false,
            qualityLevel: "standard",
            stylePreference: "modern"
          }
        };

        // 5. 执行性能优化分析
        const result = await generationEngine.generateCode(request);

        // 6. 格式化输出
        if (outputFormat === "json") {
          return {
            content: [
              {
                type: "text",
                text: JSON.stringify(result, null, 2),
              },
            ],
          };
        }

        const report = formatPerformanceOptimizationReport(result, optimizationType, priority, outputFormat === "detailed");

        return {
          content: [
            {
              type: "text",
              text: report,
            },
          ],
        };
      } catch (error) {
        return {
          content: [
            {
              type: "text",
              text: `❌ 性能优化建议生成失败: ${error instanceof Error ? error.message : String(error)}`,
            },
          ],
        };
      }
    }
  );

  console.log(`✅ 4 code generation tools registered successfully`);
}

// ============================================================================
// 报告格式化函数
// ============================================================================

function formatCodeGenerationReport(result: any, type: string, description: string): string {
  const { generatedCode, qualityAssessment, suggestions, statistics, nextSteps } = result;

  let report = `# 🚀 代码生成报告\n\n`;
  report += `**生成类型**: ${type}\n`;
  report += `**描述**: ${description}\n`;
  report += `**生成时间**: ${new Date().toLocaleString()}\n\n`;

  // 生成统计
  report += `## 📊 生成统计\n\n`;
  report += `- **文件数量**: ${statistics.filesGenerated}\n`;
  report += `- **代码行数**: ${statistics.linesGenerated}\n`;
  report += `- **生成时间**: ${statistics.generationTime}ms\n\n`;

  // 质量评估
  report += `## 🎯 质量评估\n\n`;
  report += `- **总体评分**: ${Math.round(qualityAssessment.overallScore * 100)}%\n`;
  report += `- **质量等级**: ${qualityAssessment.grade}\n\n`;

  report += `**各维度评分**:\n`;
  report += `- 可读性: ${Math.round(qualityAssessment.dimensions.readability * 100)}%\n`;
  report += `- 可维护性: ${Math.round(qualityAssessment.dimensions.maintainability * 100)}%\n`;
  report += `- 性能: ${Math.round(qualityAssessment.dimensions.performance * 100)}%\n`;
  report += `- 安全性: ${Math.round(qualityAssessment.dimensions.security * 100)}%\n`;
  report += `- 可测试性: ${Math.round(qualityAssessment.dimensions.testability * 100)}%\n\n`;

  // 生成的代码
  report += `## 📝 生成的代码\n\n`;
  generatedCode.forEach((code: any, index: number) => {
    report += `### ${index + 1}. ${code.filePath}\n\n`;
    report += `**类型**: ${code.type}\n`;
    report += `**操作**: ${code.changeType}\n\n`;
    report += `\`\`\`${getLanguageFromPath(code.filePath)}\n`;
    report += code.content;
    report += `\n\`\`\`\n\n`;
  });

  // 改进建议
  if (qualityAssessment.improvements.length > 0) {
    report += `## 💡 改进建议\n\n`;
    qualityAssessment.improvements.forEach((improvement: string, index: number) => {
      report += `${index + 1}. ${improvement}\n`;
    });
    report += `\n`;
  }

  // 后续步骤
  if (nextSteps.length > 0) {
    report += `## 🎯 后续步骤\n\n`;
    nextSteps.forEach((step: any, index: number) => {
      report += `${index + 1}. **${step.description}**\n`;
      report += `   - 类型: ${step.type}\n`;
      report += `   - 优先级: ${step.priority}\n`;
      report += `   - 预估时间: ${step.estimatedTime}\n\n`;
    });
  }

  return report;
}

function formatRefactorSuggestionsReport(result: any, refactorType: string, priority: string, detailed: boolean): string {
  let report = `# 🔄 重构建议报告\n\n`;
  report += `**重构类型**: ${refactorType}\n`;
  report += `**优先级过滤**: ${priority}\n`;
  report += `**生成时间**: ${new Date().toLocaleString()}\n\n`;

  // 这里需要根据实际的重构结果格式化
  // 简化实现
  report += `## 📋 重构建议\n\n`;
  report += `暂未实现具体的重构建议格式化逻辑。\n`;
  report += `请参考生成的 JSON 输出获取详细信息。\n\n`;

  return report;
}

function formatTestGenerationReport(result: any, testTypes: string[], framework: string, coverageTarget: number, detailed: boolean): string {
  let report = `# 🧪 测试生成报告\n\n`;
  report += `**测试类型**: ${testTypes.join(', ')}\n`;
  report += `**测试框架**: ${framework}\n`;
  report += `**覆盖率目标**: ${coverageTarget}%\n`;
  report += `**生成时间**: ${new Date().toLocaleString()}\n\n`;

  // 这里需要根据实际的测试生成结果格式化
  // 简化实现
  report += `## 📋 生成的测试\n\n`;
  report += `暂未实现具体的测试生成格式化逻辑。\n`;
  report += `请参考生成的 JSON 输出获取详细信息。\n\n`;

  return report;
}

function formatPerformanceOptimizationReport(result: any, optimizationType: string, priority: string, detailed: boolean): string {
  let report = `# ⚡ 性能优化报告\n\n`;
  report += `**优化类型**: ${optimizationType}\n`;
  report += `**优先级过滤**: ${priority}\n`;
  report += `**生成时间**: ${new Date().toLocaleString()}\n\n`;

  // 这里需要根据实际的性能优化结果格式化
  // 简化实现
  report += `## 📋 优化建议\n\n`;
  report += `暂未实现具体的性能优化格式化逻辑。\n`;
  report += `请参考生成的 JSON 输出获取详细信息。\n\n`;

  return report;
}

function getLanguageFromPath(filePath: string): string {
  const ext = filePath.split('.').pop()?.toLowerCase();
  const languageMap: Record<string, string> = {
    'ts': 'typescript',
    'js': 'javascript',
    'py': 'python',
    'java': 'java',
    'go': 'go',
    'rs': 'rust'
  };
  return languageMap[ext || ''] || 'text';
}
