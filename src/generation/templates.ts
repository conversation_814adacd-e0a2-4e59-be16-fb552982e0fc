/**
 * 智能代码生成 - 模板管理系统
 * 提供动态代码模板和渲染功能
 */

import { CodeTemplate, TemplateVariable, ProgrammingLanguage, GenerationType } from "./types.js";

/**
 * 模板注册表
 */
const templateRegistry = new Map<string, CodeTemplate>();

/**
 * 初始化模板系统
 */
export function initializeTemplates() {
  console.log(`🎨 Initializing code templates...`);

  // 注册 TypeScript 模板
  registerTypeScriptTemplates();

  // 注册 JavaScript 模板
  registerJavaScriptTemplates();

  // 注册测试模板
  registerTestTemplates();

  console.log(`✅ ${templateRegistry.size} templates registered`);
}

/**
 * 获取代码模板
 */
export async function getCodeTemplate(
  templateType: string,
  language: ProgrammingLanguage
): Promise<CodeTemplate> {
  const templateKey = `${language}-${templateType}`;
  const template = templateRegistry.get(templateKey);

  if (!template) {
    throw new Error(`Template not found: ${templateKey}`);
  }

  return template;
}

/**
 * 渲染模板
 */
export async function renderTemplate(
  template: CodeTemplate,
  variables: Record<string, any>
): Promise<string> {
  console.log(`🎨 Rendering template: ${template.name}`);

  let renderedCode = template.template;

  // 替换模板变量
  for (const variable of template.variables) {
    const value = variables[variable.name] ?? variable.defaultValue;

    if (variable.required && value === undefined) {
      throw new Error(`Required template variable missing: ${variable.name}`);
    }

    // 执行变量替换
    const placeholder = `{{${variable.name}}}`;
    const stringValue = formatVariableValue(value, variable.type);
    renderedCode = renderedCode.replace(new RegExp(placeholder, 'g'), stringValue);
  }

  // 处理条件块
  renderedCode = processConditionalBlocks(renderedCode, variables);

  // 处理循环块
  renderedCode = processLoopBlocks(renderedCode, variables);

  // 格式化代码
  renderedCode = formatGeneratedCode(renderedCode);

  console.log(`✅ Template rendered successfully`);
  return renderedCode;
}

/**
 * 注册 TypeScript 模板
 */
function registerTypeScriptTemplates() {
  // TypeScript 函数模板
  registerTemplate({
    name: "TypeScript Function",
    description: "TypeScript function template with JSDoc",
    template: `/**
 * {{description}}
 {{#if parameters.length}}
 {{#each parameters}}
 * @param {{name}} {{description}}
 {{/each}}
 {{/if}}
 {{#if returnType}}
 * @returns {{returnDescription}}
 {{/if}}
 */
{{#if isAsync}}async {{/if}}function {{name}}({{#each parameters}}{{name}}: {{type}}{{#unless @last}}, {{/unless}}{{/each}}){{#if returnType}}: {{returnType}}{{/if}} {
  {{#if isAsync}}
  // Implement async function logic
  throw new Error('Function {{name}} not yet implemented');
  {{else}}
  // Implement function logic
  throw new Error('Function {{name}} not yet implemented');
  {{/if}}
}`,
    variables: [
      { name: "name", type: "string", required: true, description: "Function name" },
      { name: "description", type: "string", required: true, description: "Function description" },
      { name: "parameters", type: "array", required: false, defaultValue: [], description: "Function parameters" },
      { name: "returnType", type: "string", required: false, description: "Return type" },
      { name: "returnDescription", type: "string", required: false, description: "Return description" },
      { name: "isAsync", type: "boolean", required: false, defaultValue: false, description: "Is async function" }
    ],
    languages: ["typescript"],
    scenarios: ["function"]
  });

  // TypeScript 类模板
  registerTemplate({
    name: "TypeScript Class",
    description: "TypeScript class template with properties and methods",
    template: `/**
 * {{description}}
 */
export class {{name}}{{#if extends}} extends {{extends}}{{/if}}{{#if implements}} implements {{implements}}{{/if}} {
  {{#each properties}}
  {{#if isPrivate}}private {{/if}}{{#if isProtected}}protected {{/if}}{{#if isReadonly}}readonly {{/if}}{{name}}: {{type}}{{#if defaultValue}} = {{defaultValue}}{{/if}};
  {{/each}}

  {{#if hasConstructor}}
  /**
   * Creates an instance of {{../name}}.
   {{#each constructorParams}}
   * @param {{name}} {{description}}
   {{/each}}
   */
  constructor({{#each constructorParams}}{{name}}: {{type}}{{#unless @last}}, {{/unless}}{{/each}}) {
    {{#if extends}}
    super({{#each superParams}}{{.}}{{#unless @last}}, {{/unless}}{{/each}});
    {{/if}}
    {{#each constructorParams}}
    this.{{name}} = {{name}};
    {{/each}}
  }
  {{/if}}

  {{#each methods}}
  /**
   * {{description}}
   {{#each parameters}}
   * @param {{name}} {{description}}
   {{/each}}
   {{#if returnType}}
   * @returns {{returnDescription}}
   {{/if}}
   */
  {{#if isPrivate}}private {{/if}}{{#if isProtected}}protected {{/if}}{{#if isStatic}}static {{/if}}{{#if isAsync}}async {{/if}}{{name}}({{#each parameters}}{{name}}: {{type}}{{#unless @last}}, {{/unless}}{{/each}}){{#if returnType}}: {{returnType}}{{/if}} {
    // Implementation for {{name}} method
    throw new Error('Method {{name}} not yet implemented');
  }

  {{/each}}
}`,
    variables: [
      { name: "name", type: "string", required: true, description: "Class name" },
      { name: "description", type: "string", required: true, description: "Class description" },
      { name: "extends", type: "string", required: false, description: "Parent class" },
      { name: "implements", type: "string", required: false, description: "Implemented interfaces" },
      { name: "properties", type: "array", required: false, defaultValue: [], description: "Class properties" },
      { name: "methods", type: "array", required: false, defaultValue: [], description: "Class methods" },
      { name: "hasConstructor", type: "boolean", required: false, defaultValue: false, description: "Has constructor" },
      { name: "constructorParams", type: "array", required: false, defaultValue: [], description: "Constructor parameters" },
      { name: "superParams", type: "array", required: false, defaultValue: [], description: "Super constructor parameters" }
    ],
    languages: ["typescript"],
    scenarios: ["class"]
  });

  // TypeScript 接口模板
  registerTemplate({
    name: "TypeScript Interface",
    description: "TypeScript interface template",
    template: `/**
 * {{description}}
 */
export interface {{name}}{{#if extends}} extends {{extends}}{{/if}} {
  {{#each properties}}
  /** {{description}} */
  {{#if isOptional}}{{name}}?: {{type}};{{else}}{{name}}: {{type}};{{/if}}
  {{/each}}

  {{#each methods}}
  /**
   * {{description}}
   {{#each parameters}}
   * @param {{name}} {{description}}
   {{/each}}
   {{#if returnType}}
   * @returns {{returnDescription}}
   {{/if}}
   */
  {{name}}({{#each parameters}}{{name}}: {{type}}{{#unless @last}}, {{/unless}}{{/each}}){{#if returnType}}: {{returnType}}{{/if}};
  {{/each}}
}`,
    variables: [
      { name: "name", type: "string", required: true, description: "Interface name" },
      { name: "description", type: "string", required: true, description: "Interface description" },
      { name: "extends", type: "string", required: false, description: "Extended interfaces" },
      { name: "properties", type: "array", required: false, defaultValue: [], description: "Interface properties" },
      { name: "methods", type: "array", required: false, defaultValue: [], description: "Interface methods" }
    ],
    languages: ["typescript"],
    scenarios: ["interface"]
  });

  // React 组件模板
  registerTemplate({
    name: "React Component",
    description: "React functional component with TypeScript",
    template: `import React{{#if hasState}}, { useState, useEffect }{{/if}} from 'react';
{{#each imports}}
import {{.}};
{{/each}}

/**
 * Props for {{name}} component
 */
export interface {{name}}Props {
  {{#each props}}
  /** {{description}} */
  {{#if isOptional}}{{name}}?: {{type}};{{else}}{{name}}: {{type}};{{/if}}
  {{/each}}
}

/**
 * {{description}}
 */
export const {{name}}: React.FC<{{name}}Props> = ({
  {{#each props}}{{name}}{{#if defaultValue}} = {{defaultValue}}{{/if}}{{#unless @last}},{{/unless}}
  {{/each}}
}) => {
  {{#each state}}
  const [{{name}}, set{{capitalize name}}] = useState<{{type}}>({{defaultValue}});
  {{/each}}

  {{#if hasEffects}}
  useEffect(() => {
    // Implement component effects
  }, []);
  {{/if}}

  {{#each handlers}}
  const {{name}} = {{#if isAsync}}async {{/if}}({{#each parameters}}{{name}}: {{type}}{{#unless @last}}, {{/unless}}{{/each}}) => {
    // Implement {{name}} handler
  };

  {{/each}}

  return (
    <div className="{{kebabCase name}}">
      {/* Implement component JSX */}
      <h1>{{name}} Component</h1>
    </div>
  );
};

export default {{name}};`,
    variables: [
      { name: "name", type: "string", required: true, description: "Component name" },
      { name: "description", type: "string", required: true, description: "Component description" },
      { name: "props", type: "array", required: false, defaultValue: [], description: "Component props" },
      { name: "state", type: "array", required: false, defaultValue: [], description: "Component state" },
      { name: "handlers", type: "array", required: false, defaultValue: [], description: "Event handlers" },
      { name: "imports", type: "array", required: false, defaultValue: [], description: "Additional imports" },
      { name: "hasState", type: "boolean", required: false, defaultValue: false, description: "Has state hooks" },
      { name: "hasEffects", type: "boolean", required: false, defaultValue: false, description: "Has effect hooks" }
    ],
    languages: ["typescript"],
    scenarios: ["component"]
  });

  // Express API 路由模板
  registerTemplate({
    name: "Express API Route",
    description: "Express.js API route with TypeScript",
    template: `import { Request, Response, NextFunction } from 'express';
{{#each imports}}
import {{.}};
{{/each}}

/**
 * {{description}}
 */
export const {{name}} = async (req: Request, res: Response, next: NextFunction) => {
  try {
    {{#each validations}}
    // Validate {{field}}
    if (!req.{{source}}.{{field}}) {
      return res.status(400).json({ error: '{{field}} is required' });
    }
    {{/each}}

    {{#each parameters}}
    const {{name}} = req.{{source}}.{{field}};
    {{/each}}

    // Process {{name}} logic
    const result = await process{{capitalize name}}({{#each parameters}}{{name}}{{#unless @last}}, {{/unless}}{{/each}});

    res.status({{successStatus}}).json({
      success: true,
      data: result
    });
  } catch (error) {
    console.error('Error in {{name}}:', error);
    next(error);
  }
};

/**
 * Process {{name}} business logic
 */
async function process{{capitalize name}}({{#each parameters}}{{name}}: {{type}}{{#unless @last}}, {{/unless}}{{/each}}) {
  // Implement business logic for {{name}}
  return { message: '{{name}} completed successfully' };
}`,
    variables: [
      { name: "name", type: "string", required: true, description: "Route handler name" },
      { name: "description", type: "string", required: true, description: "Route description" },
      { name: "parameters", type: "array", required: false, defaultValue: [], description: "Route parameters" },
      { name: "validations", type: "array", required: false, defaultValue: [], description: "Input validations" },
      { name: "imports", type: "array", required: false, defaultValue: [], description: "Additional imports" },
      { name: "successStatus", type: "number", required: false, defaultValue: 200, description: "Success status code" }
    ],
    languages: ["typescript"],
    scenarios: ["function", "api"]
  });

  // 数据库模型模板
  registerTemplate({
    name: "Database Model",
    description: "Database model with TypeScript",
    template: `{{#if useMongoose}}
import { Schema, model, Document } from 'mongoose';
{{else}}
import { Entity, Column, PrimaryGeneratedColumn, CreateDateColumn, UpdateDateColumn } from 'typeorm';
{{/if}}

/**
 * {{description}}
 */
{{#if useMongoose}}
export interface I{{name}} extends Document {
  {{#each fields}}
  {{name}}: {{type}};
  {{/each}}
  createdAt: Date;
  updatedAt: Date;
}

const {{name}}Schema = new Schema<I{{name}}>({
  {{#each fields}}
  {{name}}: {
    type: {{mongooseType}},
    required: {{required}},
    {{#if unique}}unique: true,{{/if}}
    {{#if defaultValue}}default: {{defaultValue}},{{/if}}
  },
  {{/each}}
}, {
  timestamps: true
});

export const {{name}} = model<I{{name}}>('{{name}}', {{name}}Schema);
{{else}}
@Entity('{{tableName}}')
export class {{name}} {
  @PrimaryGeneratedColumn()
  id: number;

  {{#each fields}}
  @Column({{#if columnOptions}}{{{columnOptions}}}{{/if}})
  {{name}}: {{type}};

  {{/each}}
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
{{/if}}`,
    variables: [
      { name: "name", type: "string", required: true, description: "Model name" },
      { name: "description", type: "string", required: true, description: "Model description" },
      { name: "fields", type: "array", required: false, defaultValue: [], description: "Model fields" },
      { name: "useMongoose", type: "boolean", required: false, defaultValue: true, description: "Use Mongoose (MongoDB)" },
      { name: "tableName", type: "string", required: false, description: "Database table name" }
    ],
    languages: ["typescript"],
    scenarios: ["class", "model"]
  });
}

/**
 * 注册 JavaScript 模板
 */
function registerJavaScriptTemplates() {
  // JavaScript 函数模板
  registerTemplate({
    name: "JavaScript Function",
    description: "JavaScript function template with JSDoc",
    template: `/**
 * {{description}}
 {{#each parameters}}
 * @param {{{type}}} {{name}} {{description}}
 {{/each}}
 {{#if returnType}}
 * @returns {{{returnType}}} {{returnDescription}}
 {{/if}}
 */
{{#if isAsync}}async {{/if}}function {{name}}({{#each parameters}}{{name}}{{#unless @last}}, {{/unless}}{{/each}}) {
  {{#if isAsync}}
  // Implement async function logic
  throw new Error('Function {{name}} not yet implemented');
  {{else}}
  // Implement function logic
  throw new Error('Function {{name}} not yet implemented');
  {{/if}}
}`,
    variables: [
      { name: "name", type: "string", required: true, description: "Function name" },
      { name: "description", type: "string", required: true, description: "Function description" },
      { name: "parameters", type: "array", required: false, defaultValue: [], description: "Function parameters" },
      { name: "returnType", type: "string", required: false, description: "Return type" },
      { name: "returnDescription", type: "string", required: false, description: "Return description" },
      { name: "isAsync", type: "boolean", required: false, defaultValue: false, description: "Is async function" }
    ],
    languages: ["javascript"],
    scenarios: ["function"]
  });

  // JavaScript 类模板
  registerTemplate({
    name: "JavaScript Class",
    description: "JavaScript ES6 class template",
    template: `/**
 * {{description}}
 */
class {{name}}{{#if extends}} extends {{extends}}{{/if}} {
  {{#if hasConstructor}}
  /**
   * Creates an instance of {{../name}}.
   {{#each constructorParams}}
   * @param {{{type}}} {{name}} {{description}}
   {{/each}}
   */
  constructor({{#each constructorParams}}{{name}}{{#unless @last}}, {{/unless}}{{/each}}) {
    {{#if extends}}
    super({{#each superParams}}{{.}}{{#unless @last}}, {{/unless}}{{/each}});
    {{/if}}
    {{#each constructorParams}}
    this.{{name}} = {{name}};
    {{/each}}
  }
  {{/if}}

  {{#each methods}}
  /**
   * {{description}}
   {{#each parameters}}
   * @param {{{type}}} {{name}} {{description}}
   {{/each}}
   {{#if returnType}}
   * @returns {{{returnType}}} {{returnDescription}}
   {{/if}}
   */
  {{#if isStatic}}static {{/if}}{{#if isAsync}}async {{/if}}{{name}}({{#each parameters}}{{name}}{{#unless @last}}, {{/unless}}{{/each}}) {
    // Implementation for {{name}} method
    throw new Error('Method {{name}} not yet implemented');
  }

  {{/each}}
}

module.exports = {{name}};`,
    variables: [
      { name: "name", type: "string", required: true, description: "Class name" },
      { name: "description", type: "string", required: true, description: "Class description" },
      { name: "extends", type: "string", required: false, description: "Parent class" },
      { name: "methods", type: "array", required: false, defaultValue: [], description: "Class methods" },
      { name: "hasConstructor", type: "boolean", required: false, defaultValue: false, description: "Has constructor" },
      { name: "constructorParams", type: "array", required: false, defaultValue: [], description: "Constructor parameters" },
      { name: "superParams", type: "array", required: false, defaultValue: [], description: "Super constructor parameters" }
    ],
    languages: ["javascript"],
    scenarios: ["class"]
  });
}

/**
 * 注册测试模板
 */
function registerTestTemplates() {
  // Jest 测试模板
  registerTemplate({
    name: "Jest Test Suite",
    description: "Jest test suite template",
    template: `import { {{testTarget}} } from '{{importPath}}';
{{#each additionalImports}}
import {{.}};
{{/each}}

describe('{{testTarget}}', () => {
  {{#each setupBlocks}}
  {{type}}(() => {
    {{code}}
  });

  {{/each}}
  {{#each testCases}}
  {{#if isAsync}}
  it('{{description}}', async () => {
    // Arrange
    {{arrange}}

    // Act
    {{#if isAsync}}
    const result = await {{../testTarget}}({{parameters}});
    {{else}}
    const result = {{../testTarget}}({{parameters}});
    {{/if}}

    // Assert
    {{assertions}}
  });
  {{else}}
  it('{{description}}', () => {
    // Arrange
    {{arrange}}

    // Act
    const result = {{../testTarget}}({{parameters}});

    // Assert
    {{assertions}}
  });
  {{/if}}

  {{/each}}
  {{#each errorTestCases}}
  it('{{description}}', {{#if isAsync}}async {{/if}}() => {
    // Arrange
    {{arrange}}

    // Act & Assert
    {{#if isAsync}}
    await expect({{../testTarget}}({{parameters}})).{{expectation}};
    {{else}}
    expect(() => {{../testTarget}}({{parameters}})).{{expectation}};
    {{/if}}
  });

  {{/each}}
});`,
    variables: [
      { name: "testTarget", type: "string", required: true, description: "Target function/class to test" },
      { name: "importPath", type: "string", required: true, description: "Import path for test target" },
      { name: "additionalImports", type: "array", required: false, defaultValue: [], description: "Additional imports" },
      { name: "setupBlocks", type: "array", required: false, defaultValue: [], description: "Setup blocks (beforeEach, etc.)" },
      { name: "testCases", type: "array", required: false, defaultValue: [], description: "Test cases" },
      { name: "errorTestCases", type: "array", required: false, defaultValue: [], description: "Error test cases" }
    ],
    languages: ["typescript", "javascript"],
    scenarios: ["test"]
  });
}

/**
 * 注册模板
 */
function registerTemplate(template: CodeTemplate) {
  template.languages.forEach(language => {
    template.scenarios.forEach(scenario => {
      const key = `${language}-${scenario}`;
      templateRegistry.set(key, template);
    });
  });
}

/**
 * 格式化变量值
 */
function formatVariableValue(value: any, type: string): string {
  if (value === undefined || value === null) {
    return '';
  }

  switch (type) {
    case 'string':
      return String(value);
    case 'number':
      return String(value);
    case 'boolean':
      return String(value);
    case 'array':
      return Array.isArray(value) ? value.join(', ') : String(value);
    case 'object':
      return typeof value === 'object' ? JSON.stringify(value) : String(value);
    default:
      return String(value);
  }
}

/**
 * 处理条件块
 */
function processConditionalBlocks(template: string, variables: Record<string, any>): string {
  // 简单的条件块处理 {{#if condition}}...{{/if}}
  const ifRegex = /\{\{#if\s+(\w+)\}\}([\s\S]*?)\{\{\/if\}\}/g;

  return template.replace(ifRegex, (match, condition, content) => {
    const value = variables[condition];
    return value ? content : '';
  });
}

/**
 * 处理循环块
 */
function processLoopBlocks(template: string, variables: Record<string, any>): string {
  // 简单的循环块处理 {{#each array}}...{{/each}}
  const eachRegex = /\{\{#each\s+(\w+)\}\}([\s\S]*?)\{\{\/each\}\}/g;

  return template.replace(eachRegex, (match, arrayName, content) => {
    const array = variables[arrayName];
    if (!Array.isArray(array)) {
      return '';
    }

    return array.map((item, index) => {
      let itemContent = content;

      // 替换 {{.}} 为当前项
      itemContent = itemContent.replace(/\{\{\.\}\}/g, String(item));

      // 替换 {{@index}} 为索引
      itemContent = itemContent.replace(/\{\{@index\}\}/g, String(index));

      // 替换 {{@last}} 为是否最后一项
      itemContent = itemContent.replace(/\{\{@last\}\}/g, String(index === array.length - 1));

      // 如果 item 是对象，替换其属性
      if (typeof item === 'object' && item !== null) {
        Object.keys(item).forEach(key => {
          const placeholder = new RegExp(`\\{\\{${key}\\}\\}`, 'g');
          itemContent = itemContent.replace(placeholder, String(item[key]));
        });
      }

      return itemContent;
    }).join('');
  });
}

/**
 * 格式化生成的代码
 */
function formatGeneratedCode(code: string): string {
  // 移除多余的空行
  code = code.replace(/\n\s*\n\s*\n/g, '\n\n');

  // 移除行首尾空格
  code = code.split('\n').map(line => line.trimRight()).join('\n');

  // 移除开头和结尾的空行
  code = code.trim();

  return code;
}

// 初始化模板系统
initializeTemplates();
