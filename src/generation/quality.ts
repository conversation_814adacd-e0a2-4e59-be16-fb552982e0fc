/**
 * 智能代码生成 - 质量评估器
 * 评估生成代码的质量并提供改进建议
 */

import { CodebaseAnalysis } from "../analysis/types.js";
import { GeneratedCode, QualityAssessment } from "./types.js";

/**
 * 评估生成代码质量
 */
export async function assessGeneratedCodeQuality(
  generatedCode: GeneratedCode[],
  codebaseAnalysis: CodebaseAnalysis
): Promise<QualityAssessment> {
  console.log(`🔍 Assessing quality of ${generatedCode.length} generated files...`);

  const qualityScores = {
    readability: 0,
    maintainability: 0,
    performance: 0,
    security: 0,
    testability: 0
  };

  const improvements: string[] = [];
  let totalScore = 0;

  for (const code of generatedCode) {
    const fileQuality = await assessFileQuality(code, codebaseAnalysis);

    // 累加各维度分数
    qualityScores.readability += fileQuality.readability;
    qualityScores.maintainability += fileQuality.maintainability;
    qualityScores.performance += fileQuality.performance;
    qualityScores.security += fileQuality.security;
    qualityScores.testability += fileQuality.testability;

    improvements.push(...fileQuality.improvements);
  }

  // 计算平均分数
  const fileCount = generatedCode.length;
  if (fileCount > 0) {
    qualityScores.readability /= fileCount;
    qualityScores.maintainability /= fileCount;
    qualityScores.performance /= fileCount;
    qualityScores.security /= fileCount;
    qualityScores.testability /= fileCount;
  }

  // 计算总体分数
  totalScore = (
    qualityScores.readability +
    qualityScores.maintainability +
    qualityScores.performance +
    qualityScores.security +
    qualityScores.testability
  ) / 5;

  // 确定等级
  const grade = calculateGrade(totalScore);

  // 去重改进建议
  const uniqueImprovements = [...new Set(improvements)];

  console.log(`✅ Quality assessment complete - Grade: ${grade}, Score: ${Math.round(totalScore * 100)}%`);

  return {
    overallScore: Math.round(totalScore * 100) / 100,
    dimensions: qualityScores,
    grade,
    improvements: uniqueImprovements
  };
}

/**
 * 评估单个文件质量
 */
async function assessFileQuality(
  code: GeneratedCode,
  codebaseAnalysis: CodebaseAnalysis
): Promise<{
  readability: number;
  maintainability: number;
  performance: number;
  security: number;
  testability: number;
  improvements: string[];
}> {
  const improvements: string[] = [];

  // 可读性评估
  const readability = assessReadability(code, improvements);

  // 可维护性评估
  const maintainability = assessMaintainability(code, improvements);

  // 性能评估
  const performance = assessPerformance(code, improvements);

  // 安全性评估
  const security = assessSecurity(code, improvements);

  // 可测试性评估
  const testability = assessTestability(code, improvements);

  return {
    readability,
    maintainability,
    performance,
    security,
    testability,
    improvements
  };
}

/**
 * 评估可读性
 */
function assessReadability(code: GeneratedCode, improvements: string[]): number {
  let score = 1.0;
  const lines = code.content.split('\n');

  // 检查注释覆盖率
  const commentLines = lines.filter(line =>
    line.trim().startsWith('//') ||
    line.trim().startsWith('/*') ||
    line.trim().startsWith('*')
  ).length;

  const codeLines = lines.filter(line =>
    line.trim() &&
    !line.trim().startsWith('//') &&
    !line.trim().startsWith('/*') &&
    !line.trim().startsWith('*')
  ).length;

  const commentRatio = codeLines > 0 ? commentLines / codeLines : 0;

  if (commentRatio < 0.1) {
    score -= 0.2;
    improvements.push("增加代码注释以提高可读性");
  }

  // 检查函数长度
  const functionMatches = code.content.match(/function\s+\w+\s*\([^)]*\)\s*\{/g) || [];
  for (const match of functionMatches) {
    const functionStart = code.content.indexOf(match);
    const functionBody = extractFunctionBody(code.content, functionStart);
    const functionLines = functionBody.split('\n').length;

    if (functionLines > 50) {
      score -= 0.1;
      improvements.push("考虑将长函数分解为更小的函数");
    }
  }

  // 检查变量命名
  const variableMatches = code.content.match(/(?:let|const|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)/g) || [];
  for (const match of variableMatches) {
    const varName = match.split(/\s+/)[1];
    if (varName.length < 3 && !['i', 'j', 'k', 'id'].includes(varName)) {
      score -= 0.05;
      improvements.push("使用更具描述性的变量名");
      break; // 只提示一次
    }
  }

  // 检查行长度
  const longLines = lines.filter(line => line.length > 120).length;
  if (longLines > 0) {
    score -= 0.1;
    improvements.push("将过长的代码行分解为多行");
  }

  return Math.max(0, score);
}

/**
 * Assess maintainability
 */
function assessMaintainability(code: GeneratedCode, improvements: string[]): number {
  let score = 1.0;

  // Check cyclomatic complexity
  const complexity = calculateCyclomaticComplexity(code.content);
  if (complexity > 10) {
    score -= 0.3;
    improvements.push("Reduce code cyclomatic complexity");
  } else if (complexity > 5) {
    score -= 0.1;
    improvements.push("Consider simplifying code logic");
  }

  // Check duplicate code
  const duplicateBlocks = findDuplicateCodeBlocks(code.content);
  if (duplicateBlocks.length > 0) {
    score -= 0.2;
    improvements.push("Extract duplicate code to common functions");
  }

  // Check hardcoded values
  const hardcodedValues = findHardcodedValues(code.content);
  if (hardcodedValues.length > 3) {
    score -= 0.15;
    improvements.push("Extract hardcoded values to constants");
  }

  // 检查错误处理
  const hasErrorHandling = code.content.includes('try') ||
    code.content.includes('catch') ||
    code.content.includes('throw');

  if (!hasErrorHandling && code.content.includes('async')) {
    score -= 0.2;
    improvements.push("为异步操作添加错误处理");
  }

  return Math.max(0, score);
}

/**
 * 评估性能
 */
function assessPerformance(code: GeneratedCode, improvements: string[]): number {
  let score = 1.0;

  // 检查低效的循环
  const nestedLoops = (code.content.match(/for\s*\([^}]*for\s*\(/g) || []).length;
  if (nestedLoops > 0) {
    score -= 0.2;
    improvements.push("考虑优化嵌套循环的性能");
  }

  // 检查同步文件操作
  if (code.content.includes('readFileSync') || code.content.includes('writeFileSync')) {
    score -= 0.3;
    improvements.push("使用异步文件操作以提高性能");
  }

  // 检查未优化的数组操作
  const inefficientArrayOps = code.content.match(/\.forEach\s*\([^}]*\.push\s*\(/g) || [];
  if (inefficientArrayOps.length > 0) {
    score -= 0.1;
    improvements.push("考虑使用 map() 替代 forEach() + push()");
  }

  // 检查内存泄漏风险
  if (code.content.includes('setInterval') && !code.content.includes('clearInterval')) {
    score -= 0.2;
    improvements.push("确保清理定时器以避免内存泄漏");
  }

  return Math.max(0, score);
}

/**
 * 评估安全性
 */
function assessSecurity(code: GeneratedCode, improvements: string[]): number {
  let score = 1.0;

  // 检查 SQL 注入风险
  if (code.content.includes('SELECT') && code.content.includes('+')) {
    score -= 0.4;
    improvements.push("使用参数化查询防止 SQL 注入");
  }

  // 检查 XSS 风险
  if (code.content.includes('innerHTML') || code.content.includes('dangerouslySetInnerHTML')) {
    score -= 0.3;
    improvements.push("验证和转义用户输入以防止 XSS 攻击");
  }

  // 检查敏感信息泄露
  const sensitivePatterns = [
    /password\s*[:=]\s*["'][^"']*["']/i,
    /api[_-]?key\s*[:=]\s*["'][^"']*["']/i,
    /secret\s*[:=]\s*["'][^"']*["']/i
  ];

  for (const pattern of sensitivePatterns) {
    if (pattern.test(code.content)) {
      score -= 0.3;
      improvements.push("避免在代码中硬编码敏感信息");
      break;
    }
  }

  // 检查输入验证
  if (code.content.includes('req.body') || code.content.includes('req.query')) {
    if (!code.content.includes('validate') && !code.content.includes('sanitize')) {
      score -= 0.2;
      improvements.push("添加输入验证和清理");
    }
  }

  return Math.max(0, score);
}

/**
 * 评估可测试性
 */
function assessTestability(code: GeneratedCode, improvements: string[]): number {
  let score = 1.0;

  // 检查函数纯度
  const functions = extractFunctions(code.content);
  let impureFunctions = 0;

  for (const func of functions) {
    if (func.includes('console.') ||
      func.includes('Date.now()') ||
      func.includes('Math.random()') ||
      func.includes('process.env')) {
      impureFunctions++;
    }
  }

  if (impureFunctions > 0) {
    score -= 0.2;
    improvements.push("减少副作用以提高函数的可测试性");
  }

  // 检查依赖注入
  const hasHardcodedDependencies = code.content.includes('new ') &&
    !code.content.includes('constructor');

  if (hasHardcodedDependencies) {
    score -= 0.15;
    improvements.push("使用依赖注入提高可测试性");
  }

  // 检查是否有对应的测试文件
  if (code.type !== 'test' && !code.filePath.includes('.test.') && !code.filePath.includes('.spec.')) {
    // 这里应该检查是否有对应的测试文件，简化处理
    score -= 0.1;
    improvements.push("为生成的代码添加单元测试");
  }

  return Math.max(0, score);
}

/**
 * 计算等级
 */
function calculateGrade(score: number): "A" | "B" | "C" | "D" | "F" {
  if (score >= 0.9) return "A";
  if (score >= 0.8) return "B";
  if (score >= 0.7) return "C";
  if (score >= 0.6) return "D";
  return "F";
}

/**
 * 提取函数体
 */
function extractFunctionBody(code: string, startIndex: number): string {
  let braceCount = 0;
  let inFunction = false;
  let functionBody = '';

  for (let i = startIndex; i < code.length; i++) {
    const char = code[i];

    if (char === '{') {
      braceCount++;
      inFunction = true;
    } else if (char === '}') {
      braceCount--;
    }

    if (inFunction) {
      functionBody += char;
    }

    if (inFunction && braceCount === 0) {
      break;
    }
  }

  return functionBody;
}

/**
 * 计算圈复杂度
 */
function calculateCyclomaticComplexity(code: string): number {
  let complexity = 1; // 基础复杂度

  // 计算决策点
  const decisionPoints = [
    /\bif\b/g,
    /\belse\s+if\b/g,
    /\bwhile\b/g,
    /\bfor\b/g,
    /\bswitch\b/g,
    /\bcase\b/g,
    /\bcatch\b/g,
    /\b\?\s*:/g, // 三元操作符
    /\b&&\b/g,
    /\b\|\|\b/g
  ];

  for (const pattern of decisionPoints) {
    const matches = code.match(pattern) || [];
    complexity += matches.length;
  }

  return complexity;
}

/**
 * 查找重复代码块
 */
function findDuplicateCodeBlocks(code: string): string[] {
  const lines = code.split('\n').map(line => line.trim()).filter(line => line);
  const duplicates: string[] = [];
  const blockSize = 3; // 检查3行以上的重复

  for (let i = 0; i <= lines.length - blockSize; i++) {
    const block = lines.slice(i, i + blockSize).join('\n');

    for (let j = i + blockSize; j <= lines.length - blockSize; j++) {
      const compareBlock = lines.slice(j, j + blockSize).join('\n');

      if (block === compareBlock && !duplicates.includes(block)) {
        duplicates.push(block);
      }
    }
  }

  return duplicates;
}

/**
 * Find hardcoded values
 */
function findHardcodedValues(code: string): string[] {
  const hardcoded: string[] = [];

  // 查找字符串字面量（排除空字符串和单字符）
  const stringMatches = code.match(/"[^"]{2,}"|'[^']{2,}'/g) || [];
  hardcoded.push(...stringMatches);

  // 查找数字字面量（排除0, 1, -1）
  const numberMatches = code.match(/\b(?!0\b|1\b|-1\b)\d+\b/g) || [];
  hardcoded.push(...numberMatches);

  return hardcoded;
}

/**
 * 提取函数
 */
function extractFunctions(code: string): string[] {
  const functions: string[] = [];
  const functionRegex = /function\s+\w+\s*\([^)]*\)\s*\{[^}]*\}/g;
  const arrowFunctionRegex = /(?:const|let|var)\s+\w+\s*=\s*\([^)]*\)\s*=>\s*\{[^}]*\}/g;

  const functionMatches = code.match(functionRegex) || [];
  const arrowMatches = code.match(arrowFunctionRegex) || [];

  functions.push(...functionMatches, ...arrowMatches);

  return functions;
}
