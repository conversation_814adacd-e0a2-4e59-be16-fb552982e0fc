/**
 * 智能分析引擎
 * 替代硬编码的业务逻辑，提供可配置的智能分析能力
 * v1.4.0 - Sub-Agents 革命
 */

import { SubAgentsConfigManager } from '../../config/sub-agents-config.js';

export interface AnalysisContext {
  projectRoot: string;
  description: string;
  title: string;
  domain?: string;
  technology?: string;
  complexity?: 'simple' | 'medium' | 'complex';
}

export interface AnalysisResult {
  confidence: number;
  quality: number;
  data: any;
  metadata: {
    analysisTime: number;
    method: string;
    version: string;
  };
}

/**
 * 基础分析引擎
 */
export abstract class BaseAnalysisEngine {
  protected config: any;
  protected context: AnalysisContext;

  constructor(context: AnalysisContext) {
    this.context = context;
    this.config = SubAgentsConfigManager.getInstance(context.projectRoot).getConfig();
  }

  /**
   * 执行分析
   */
  abstract analyze(): Promise<AnalysisResult>;

  /**
   * 检测项目领域
   */
  protected detectDomain(description: string): string {
    const domainKeywords = {
      'auth': ['认证', '登录', '注册', 'auth', 'login', 'register', 'user'],
      'ecommerce': ['商城', '购物', '订单', 'shop', 'order', 'cart', 'payment'],
      'cms': ['内容', '管理', '文章', 'content', 'article', 'blog'],
      'dashboard': ['仪表板', '管理后台', '数据', 'dashboard', 'admin', 'analytics'],
      'api': ['接口', 'api', 'service', 'microservice'],
      'mobile': ['移动', '手机', 'mobile', 'app', 'ios', 'android'],
      'web': ['网站', '前端', 'web', 'frontend', 'website']
    };

    const lowerDesc = description.toLowerCase();
    
    for (const [domain, keywords] of Object.entries(domainKeywords)) {
      if (keywords.some(keyword => lowerDesc.includes(keyword))) {
        return domain;
      }
    }
    
    return 'general';
  }

  /**
   * 检测技术栈偏好
   */
  protected detectTechnology(description: string): string {
    const techKeywords = {
      'react': ['react', 'jsx', 'next.js', 'nextjs'],
      'vue': ['vue', 'nuxt', 'vuejs'],
      'angular': ['angular', 'ng'],
      'node': ['node', 'nodejs', 'express', 'koa'],
      'python': ['python', 'django', 'flask', 'fastapi'],
      'java': ['java', 'spring', 'springboot'],
      'go': ['go', 'golang', 'gin'],
      'php': ['php', 'laravel', 'symfony']
    };

    const lowerDesc = description.toLowerCase();
    
    for (const [tech, keywords] of Object.entries(techKeywords)) {
      if (keywords.some(keyword => lowerDesc.includes(keyword))) {
        return tech;
      }
    }
    
    return 'javascript'; // 默认
  }

  /**
   * 评估复杂度
   */
  protected assessComplexity(description: string): 'simple' | 'medium' | 'complex' {
    const complexityIndicators = {
      simple: ['简单', '基础', 'simple', 'basic', 'crud'],
      medium: ['中等', '标准', 'medium', 'standard', 'typical'],
      complex: ['复杂', '高级', '企业级', 'complex', 'advanced', 'enterprise', 'microservice', 'distributed']
    };

    const lowerDesc = description.toLowerCase();
    
    for (const [level, indicators] of Object.entries(complexityIndicators)) {
      if (indicators.some(indicator => lowerDesc.includes(indicator))) {
        return level as 'simple' | 'medium' | 'complex';
      }
    }

    // 基于描述长度和关键词数量评估
    const wordCount = description.split(/\s+/).length;
    if (wordCount < 10) return 'simple';
    if (wordCount < 30) return 'medium';
    return 'complex';
  }

  /**
   * 计算质量分数
   */
  protected calculateQualityScore(factors: Record<string, number>): number {
    const weights = {
      completeness: 0.3,
      accuracy: 0.25,
      consistency: 0.2,
      clarity: 0.15,
      feasibility: 0.1
    };

    let totalScore = 0;
    let totalWeight = 0;

    for (const [factor, score] of Object.entries(factors)) {
      const weight = weights[factor as keyof typeof weights] || 0.1;
      totalScore += score * weight;
      totalWeight += weight;
    }

    return Math.round((totalScore / totalWeight) * 100) / 100;
  }

  /**
   * 生成分析元数据
   */
  protected generateMetadata(method: string, startTime: number): AnalysisResult['metadata'] {
    return {
      analysisTime: Date.now() - startTime,
      method,
      version: this.config.version || '1.4.0'
    };
  }
}

/**
 * 需求分析引擎
 */
export class RequirementsAnalysisEngine extends BaseAnalysisEngine {
  async analyze(): Promise<AnalysisResult> {
    const startTime = Date.now();
    
    // 检测项目特征
    const domain = this.detectDomain(this.context.description);
    const technology = this.detectTechnology(this.context.description);
    const complexity = this.assessComplexity(this.context.description);

    // 生成用户故事模板
    const userStories = await this.generateUserStories(domain, complexity);
    
    // 生成验收标准
    const acceptanceCriteria = await this.generateAcceptanceCriteria(userStories);

    // 计算质量分数
    const qualityFactors = {
      completeness: userStories.length >= 3 ? 90 : 70,
      accuracy: domain !== 'general' ? 85 : 70,
      consistency: 80,
      clarity: 85,
      feasibility: complexity === 'simple' ? 90 : complexity === 'medium' ? 80 : 70
    };

    const quality = this.calculateQualityScore(qualityFactors);

    return {
      confidence: domain !== 'general' ? 85 : 70,
      quality,
      data: {
        domain,
        technology,
        complexity,
        userStories,
        acceptanceCriteria,
        specName: this.generateSpecName(this.context.title)
      },
      metadata: this.generateMetadata('requirements-analysis', startTime)
    };
  }

  private async generateUserStories(domain: string, complexity: 'simple' | 'medium' | 'complex') {
    const templates = this.getUserStoryTemplates(domain);
    const storyCount = complexity === 'simple' ? 3 : complexity === 'medium' ? 5 : 8;
    
    return templates.slice(0, storyCount).map((template, index) => ({
      id: `US${String(index + 1).padStart(3, '0')}`,
      title: template.title,
      description: template.description,
      acceptanceCriteria: template.acceptanceCriteria,
      priority: template.priority,
      estimatedHours: template.estimatedHours
    }));
  }

  private getUserStoryTemplates(domain: string) {
    const templates = {
      auth: [
        {
          title: '用户注册',
          description: 'As a new user, I want to register an account so that I can access the system',
          acceptanceCriteria: [
            'User can enter email and password',
            'System validates email format',
            'Password meets security requirements',
            'User receives confirmation email'
          ],
          priority: 'high',
          estimatedHours: 8
        },
        {
          title: '用户登录',
          description: 'As a registered user, I want to login so that I can access my account',
          acceptanceCriteria: [
            'User can enter credentials',
            'System validates credentials',
            'User is redirected to dashboard on success',
            'Error message shown on failure'
          ],
          priority: 'high',
          estimatedHours: 6
        },
        {
          title: '密码重置',
          description: 'As a user, I want to reset my password so that I can regain access to my account',
          acceptanceCriteria: [
            'User can request password reset',
            'System sends reset email',
            'User can set new password',
            'Old password is invalidated'
          ],
          priority: 'medium',
          estimatedHours: 4
        }
      ],
      ecommerce: [
        {
          title: '商品浏览',
          description: 'As a customer, I want to browse products so that I can find items to purchase',
          acceptanceCriteria: [
            'Products are displayed in a grid',
            'User can filter by category',
            'User can search products',
            'Product details are accessible'
          ],
          priority: 'high',
          estimatedHours: 12
        },
        {
          title: '购物车管理',
          description: 'As a customer, I want to manage my cart so that I can control my purchases',
          acceptanceCriteria: [
            'User can add items to cart',
            'User can remove items from cart',
            'Cart total is calculated correctly',
            'Cart persists across sessions'
          ],
          priority: 'high',
          estimatedHours: 10
        }
      ],
      general: [
        {
          title: '基础功能',
          description: 'As a user, I want to access the main functionality so that I can accomplish my goals',
          acceptanceCriteria: [
            'User can access the main interface',
            'Core features are available',
            'System responds appropriately',
            'User receives feedback on actions'
          ],
          priority: 'high',
          estimatedHours: 8
        }
      ]
    };

    return templates[domain as keyof typeof templates] || templates.general;
  }

  private async generateAcceptanceCriteria(userStories: any[]) {
    return userStories.map(story => ({
      storyId: story.id,
      criteria: story.acceptanceCriteria,
      testScenarios: story.acceptanceCriteria.map((criterion: string, index: number) => ({
        id: `${story.id}-T${index + 1}`,
        description: `Test: ${criterion}`,
        expectedResult: 'System behaves as expected'
      }))
    }));
  }

  private generateSpecName(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);
  }
}
