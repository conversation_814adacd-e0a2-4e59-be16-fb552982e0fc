/**
 * MCP 分析引擎
 * 基于 MCP 设计，由调用的 AI 直接进行分析
 * v1.4.0 - Sub-Agents 革命
 */

import { SubAgentsConfigManager } from '../../config/sub-agents-config.js';

export interface MCPAnalysisContext {
  projectRoot: string;
  description: string;
  title: string;
  domain?: string;
  technology?: string;
  complexity?: string;
  requirements?: any;
  architecture?: any;
  implementation?: any;
  [key: string]: any;
}

export interface MCPAnalysisResult {
  confidence: number;
  quality: number;
  data: any;
  reasoning: string;
  metadata: {
    analysisTime: number;
    method: string;
    version: string;
    analysisType: string;
  };
}

/**
 * MCP 分析引擎基类
 * 由调用的 AI 直接提供分析结果
 */
export abstract class MCPAnalysisEngine {
  protected config: any;
  protected context: MCPAnalysisContext;

  constructor(context: MCPAnalysisContext) {
    this.context = context;
    this.config = SubAgentsConfigManager.getInstance(context.projectRoot).getConfig();
  }

  /**
   * 执行分析
   * 这个方法会被 MCP 调用的 AI 重写，提供实际的分析逻辑
   */
  async analyze(): Promise<MCPAnalysisResult> {
    const startTime = Date.now();
    
    try {
      // 调用具体的分析方法
      const analysisData = await this.performAnalysis();
      
      // 计算质量分数
      const quality = await this.calculateQuality(analysisData);
      
      // 计算置信度
      const confidence = this.calculateConfidence(analysisData);
      
      return {
        confidence,
        quality,
        data: analysisData,
        reasoning: this.generateReasoning(analysisData),
        metadata: {
          analysisTime: Date.now() - startTime,
          method: 'mcp-analysis',
          version: this.config.version || '1.4.0',
          analysisType: this.getAnalysisType()
        }
      };
    } catch (error) {
      console.error(`MCP analysis failed for ${this.getAnalysisType()}:`, error);
      
      // 降级到基础分析
      return await this.fallbackAnalysis(startTime);
    }
  }

  /**
   * 执行具体的分析
   * 子类必须实现这个方法
   */
  protected abstract performAnalysis(): Promise<any>;

  /**
   * 获取分析类型
   */
  protected abstract getAnalysisType(): string;

  /**
   * 计算质量分数
   */
  protected abstract calculateQuality(data: any): Promise<number>;

  /**
   * 生成分析推理
   */
  protected abstract generateReasoning(data: any): string;

  /**
   * 降级分析
   */
  protected abstract fallbackAnalysis(startTime: number): Promise<MCPAnalysisResult>;

  /**
   * 计算置信度
   */
  protected calculateConfidence(data: any): number {
    // 基于数据完整性和一致性计算置信度
    let confidence = 70; // 基础置信度
    
    if (data && typeof data === 'object') {
      const keys = Object.keys(data);
      confidence += Math.min(keys.length * 5, 20); // 数据完整性加分
    }
    
    return Math.min(confidence, 95);
  }

  /**
   * 检测项目领域
   */
  protected detectDomain(description: string): string {
    const domainKeywords = {
      'auth': ['认证', '登录', '注册', 'auth', 'login', 'register', 'user'],
      'ecommerce': ['商城', '购物', '订单', 'shop', 'order', 'cart', 'payment'],
      'cms': ['内容', '管理', '文章', 'content', 'article', 'blog'],
      'dashboard': ['仪表板', '管理后台', '数据', 'dashboard', 'admin', 'analytics'],
      'api': ['接口', 'api', 'service', 'microservice'],
      'mobile': ['移动', '手机', 'mobile', 'app', 'ios', 'android'],
      'web': ['网站', '前端', 'web', 'frontend', 'website']
    };

    const lowerDesc = description.toLowerCase();
    
    for (const [domain, keywords] of Object.entries(domainKeywords)) {
      if (keywords.some(keyword => lowerDesc.includes(keyword))) {
        return domain;
      }
    }
    
    return 'general';
  }

  /**
   * 检测技术栈偏好
   */
  protected detectTechnology(description: string): string {
    const techKeywords = {
      'react': ['react', 'jsx', 'next.js', 'nextjs'],
      'vue': ['vue', 'nuxt', 'vuejs'],
      'angular': ['angular', 'ng'],
      'node': ['node', 'nodejs', 'express', 'koa'],
      'python': ['python', 'django', 'flask', 'fastapi'],
      'java': ['java', 'spring', 'springboot'],
      'go': ['go', 'golang', 'gin'],
      'php': ['php', 'laravel', 'symfony']
    };

    const lowerDesc = description.toLowerCase();
    
    for (const [tech, keywords] of Object.entries(techKeywords)) {
      if (keywords.some(keyword => lowerDesc.includes(keyword))) {
        return tech;
      }
    }
    
    return 'javascript'; // 默认
  }

  /**
   * 评估复杂度
   */
  protected assessComplexity(description: string): 'simple' | 'medium' | 'complex' {
    const complexityIndicators = {
      simple: ['简单', '基础', 'simple', 'basic', 'crud'],
      medium: ['中等', '标准', 'medium', 'standard', 'typical'],
      complex: ['复杂', '高级', '企业级', 'complex', 'advanced', 'enterprise', 'microservice', 'distributed']
    };

    const lowerDesc = description.toLowerCase();
    
    for (const [level, indicators] of Object.entries(complexityIndicators)) {
      if (indicators.some(indicator => lowerDesc.includes(indicator))) {
        return level as 'simple' | 'medium' | 'complex';
      }
    }

    // 基于描述长度和关键词数量评估
    const wordCount = description.split(/\s+/).length;
    if (wordCount < 10) return 'simple';
    if (wordCount < 30) return 'medium';
    return 'complex';
  }
}

/**
 * MCP 需求分析引擎
 * 由调用的 AI 提供需求分析逻辑
 */
export class MCPRequirementsAnalysisEngine extends MCPAnalysisEngine {
  protected getAnalysisType(): string {
    return 'requirements';
  }

  protected async performAnalysis(): Promise<any> {
    // 这里应该由调用的 AI 提供具体的分析逻辑
    // 作为示例，我们提供一个基础的分析结构
    const domain = this.detectDomain(this.context.description);
    const technology = this.detectTechnology(this.context.description);
    const complexity = this.assessComplexity(this.context.description);

    return {
      domain,
      technology,
      complexity,
      userStories: this.generateUserStories(domain, complexity),
      acceptanceCriteria: this.generateAcceptanceCriteria(),
      specName: this.generateSpecName(this.context.title),
      riskFactors: this.identifyRiskFactors(complexity),
      qualityMetrics: this.generateQualityMetrics()
    };
  }

  protected async calculateQuality(data: any): Promise<number> {
    let score = 0;
    
    // 用户故事质量
    if (data.userStories && data.userStories.length > 0) {
      score += Math.min(data.userStories.length * 15, 40);
    }
    
    // 验收标准质量
    if (data.acceptanceCriteria && data.acceptanceCriteria.length > 0) {
      score += Math.min(data.acceptanceCriteria.length * 10, 30);
    }
    
    // 完整性检查
    const hasRequiredFields = data.userStories?.every((story: any) => 
      story.title && story.description && story.acceptanceCriteria
    );
    if (hasRequiredFields) score += 20;
    
    // 质量指标
    if (data.qualityMetrics) score += 10;
    
    return Math.min(score, 100);
  }

  protected generateReasoning(data: any): string {
    return `基于项目描述"${this.context.description}"，识别出项目领域为${data.domain}，技术栈偏好为${data.technology}，复杂度为${data.complexity}。生成了${data.userStories?.length || 0}个用户故事和相应的验收标准。`;
  }

  protected async fallbackAnalysis(startTime: number): Promise<MCPAnalysisResult> {
    const basicUserStory = {
      id: 'US001',
      title: `${this.context.title} - 核心功能`,
      description: `As a user, I want to use ${this.context.title} so that I can achieve my goals`,
      acceptanceCriteria: [
        'Core functionality is accessible',
        'User interface is intuitive',
        'System responds appropriately'
      ],
      priority: 'high',
      estimatedHours: 8
    };

    return {
      confidence: 60,
      quality: 70,
      data: {
        userStories: [basicUserStory],
        acceptanceCriteria: basicUserStory.acceptanceCriteria.map((criteria, index) => ({
          id: `AC001-${index + 1}`,
          description: criteria,
          testable: true,
          priority: 'must'
        })),
        specName: this.generateSpecName(this.context.title)
      },
      reasoning: 'Fallback analysis due to analysis failure',
      metadata: {
        analysisTime: Date.now() - startTime,
        method: 'fallback-analysis',
        version: this.config.version || '1.4.0',
        analysisType: 'requirements'
      }
    };
  }

  private generateUserStories(domain: string, complexity: 'simple' | 'medium' | 'complex') {
    // 基础用户故事生成逻辑
    const storyCount = complexity === 'simple' ? 3 : complexity === 'medium' ? 5 : 8;
    const stories = [];
    
    for (let i = 1; i <= storyCount; i++) {
      stories.push({
        id: `US${String(i).padStart(3, '0')}`,
        title: `${domain} 功能 ${i}`,
        description: `As a user, I want to access ${domain} functionality ${i}`,
        acceptanceCriteria: [
          'Functionality is accessible',
          'User receives appropriate feedback',
          'System handles errors gracefully'
        ],
        priority: i <= 2 ? 'high' : i <= 4 ? 'medium' : 'low',
        estimatedHours: 4 + i * 2
      });
    }
    
    return stories;
  }

  private generateAcceptanceCriteria() {
    return [
      {
        id: 'AC001',
        description: 'System provides clear user feedback',
        testable: true,
        priority: 'must'
      },
      {
        id: 'AC002',
        description: 'Error handling is graceful and informative',
        testable: true,
        priority: 'must'
      }
    ];
  }

  private generateSpecName(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);
  }

  private identifyRiskFactors(complexity: string) {
    const risks = ['Technical complexity may require additional time'];
    if (complexity === 'complex') {
      risks.push('Integration challenges with multiple systems');
      risks.push('Performance optimization may be required');
    }
    return risks;
  }

  private generateQualityMetrics() {
    return {
      completeness: 85,
      testability: 90,
      clarity: 80,
      feasibility: 85
    };
  }
}
