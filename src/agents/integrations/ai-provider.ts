/**
 * AI 提供商集成
 * 支持 OpenAI, Anthropic, 本地模型等
 * v1.4.0 - Sub-Agents 革命
 */

import { SubAgentsConfigManager } from '../../config/sub-agents-config.js';

export interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface AIResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model?: string;
  finishReason?: string;
}

export interface AIProviderConfig {
  provider: 'openai' | 'anthropic' | 'local';
  model: string;
  apiKey?: string;
  baseUrl?: string;
  timeout: number;
  maxTokens?: number;
  temperature?: number;
}

/**
 * AI 提供商基类
 */
export abstract class AIProvider {
  protected config: AIProviderConfig;

  constructor(config: AIProviderConfig) {
    this.config = config;
  }

  /**
   * 发送消息到 AI 模型
   */
  abstract sendMessage(messages: AIMessage[]): Promise<AIResponse>;

  /**
   * 检查 API 可用性
   */
  abstract checkHealth(): Promise<boolean>;

  /**
   * 获取支持的模型列表
   */
  abstract getSupportedModels(): string[];
}

/**
 * OpenAI 提供商
 */
export class OpenAIProvider extends AIProvider {
  async sendMessage(messages: AIMessage[]): Promise<AIResponse> {
    if (!this.config.apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const response = await fetch(this.config.baseUrl || 'https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: messages,
        max_tokens: this.config.maxTokens || 4000,
        temperature: this.config.temperature || 0.7,
      }),
      signal: AbortSignal.timeout(this.config.timeout),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    return {
      content: data.choices[0]?.message?.content || '',
      usage: data.usage,
      model: data.model,
      finishReason: data.choices[0]?.finish_reason,
    };
  }

  async checkHealth(): Promise<boolean> {
    try {
      const response = await this.sendMessage([
        { role: 'user', content: 'Hello' }
      ]);
      return !!response.content;
    } catch {
      return false;
    }
  }

  getSupportedModels(): string[] {
    return [
      'gpt-4',
      'gpt-4-turbo',
      'gpt-3.5-turbo',
      'gpt-4o',
      'gpt-4o-mini'
    ];
  }
}

/**
 * Anthropic 提供商
 */
export class AnthropicProvider extends AIProvider {
  async sendMessage(messages: AIMessage[]): Promise<AIResponse> {
    if (!this.config.apiKey) {
      throw new Error('Anthropic API key not configured');
    }

    // 转换消息格式
    const systemMessage = messages.find(m => m.role === 'system')?.content || '';
    const userMessages = messages.filter(m => m.role !== 'system');

    const response = await fetch(this.config.baseUrl || 'https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': this.config.apiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01',
      },
      body: JSON.stringify({
        model: this.config.model,
        max_tokens: this.config.maxTokens || 4000,
        system: systemMessage,
        messages: userMessages,
        temperature: this.config.temperature || 0.7,
      }),
      signal: AbortSignal.timeout(this.config.timeout),
    });

    if (!response.ok) {
      throw new Error(`Anthropic API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    return {
      content: data.content[0]?.text || '',
      usage: data.usage,
      model: data.model,
      finishReason: data.stop_reason,
    };
  }

  async checkHealth(): Promise<boolean> {
    try {
      const response = await this.sendMessage([
        { role: 'user', content: 'Hello' }
      ]);
      return !!response.content;
    } catch {
      return false;
    }
  }

  getSupportedModels(): string[] {
    return [
      'claude-3-5-sonnet-20241022',
      'claude-3-opus-20240229',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307'
    ];
  }
}

/**
 * 本地模型提供商
 */
export class LocalProvider extends AIProvider {
  async sendMessage(messages: AIMessage[]): Promise<AIResponse> {
    const response = await fetch(this.config.baseUrl || 'http://localhost:11434/api/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: messages,
        stream: false,
      }),
      signal: AbortSignal.timeout(this.config.timeout),
    });

    if (!response.ok) {
      throw new Error(`Local AI API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    return {
      content: data.message?.content || '',
      model: data.model,
    };
  }

  async checkHealth(): Promise<boolean> {
    try {
      const response = await fetch(this.config.baseUrl || 'http://localhost:11434/api/tags');
      return response.ok;
    } catch {
      return false;
    }
  }

  getSupportedModels(): string[] {
    return [
      'llama2',
      'codellama',
      'mistral',
      'qwen',
      'deepseek-coder'
    ];
  }
}

/**
 * AI 提供商工厂
 */
export class AIProviderFactory {
  static create(config: AIProviderConfig): AIProvider {
    switch (config.provider) {
      case 'openai':
        return new OpenAIProvider(config);
      case 'anthropic':
        return new AnthropicProvider(config);
      case 'local':
        return new LocalProvider(config);
      default:
        throw new Error(`Unsupported AI provider: ${config.provider}`);
    }
  }

  static createFromProjectConfig(projectRoot: string): AIProvider {
    const configManager = SubAgentsConfigManager.getInstance(projectRoot);
    const aiConfig = configManager.getConfig().integrations?.ai;

    if (!aiConfig) {
      throw new Error('AI integration not configured');
    }

    return this.create(aiConfig as AIProviderConfig);
  }
}

/**
 * AI 服务管理器
 */
export class AIService {
  private provider: AIProvider;
  private retryCount: number = 3;
  private retryDelay: number = 1000;

  constructor(provider: AIProvider) {
    this.provider = provider;
  }

  /**
   * 发送消息（带重试机制）
   */
  async sendMessage(messages: AIMessage[]): Promise<AIResponse> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= this.retryCount; attempt++) {
      try {
        console.log(`🤖 AI Request (attempt ${attempt}/${this.retryCount})`);
        const response = await this.provider.sendMessage(messages);
        console.log(`✅ AI Response received (${response.usage?.totalTokens || 'unknown'} tokens)`);
        return response;
      } catch (error) {
        lastError = error as Error;
        console.warn(`❌ AI Request failed (attempt ${attempt}/${this.retryCount}):`, error);
        
        if (attempt < this.retryCount) {
          await this.delay(this.retryDelay * attempt);
        }
      }
    }

    throw new Error(`AI service failed after ${this.retryCount} attempts: ${lastError?.message}`);
  }

  /**
   * 检查服务健康状态
   */
  async checkHealth(): Promise<boolean> {
    try {
      return await this.provider.checkHealth();
    } catch {
      return false;
    }
  }

  /**
   * 获取支持的模型
   */
  getSupportedModels(): string[] {
    return this.provider.getSupportedModels();
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 创建 AI 服务实例
   */
  static create(projectRoot: string): AIService {
    const provider = AIProviderFactory.createFromProjectConfig(projectRoot);
    return new AIService(provider);
  }
}
