/**
 * Professional Sub-Agents Workflow
 * Integrates professional prompts with existing Sub-Agents architecture
 * v1.4.0 - Sub-Agents Revolution
 */

import { ProfessionalPromptFactory } from '../engines/professional-prompt-engine.js';
import { MessageBus } from '../communication/message-bus.js';
import { AgentFactory } from '../base-agent.js';
import { 
  AgentContext, 
  AgentMessage, 
  WorkflowPhase,
  AgentType 
} from '../types.js';

export interface ProfessionalWorkflowContext {
  projectRoot: string;
  projectTitle: string;
  projectDescription: string;
  qualityThresholds: {
    requirements: number;
    architecture: number;
    implementation: number;
    quality: number;
    testing: number;
  };
  enabledAgents?: AgentType[];
  outputFormat?: 'detailed' | 'summary' | 'json';
}

export interface ProfessionalWorkflowResult {
  phase: WorkflowPhase;
  agentType: AgentType;
  success: boolean;
  result: any;
  qualityScore: number;
  promptUsed: string;
  confidence: number;
  executionTime: number;
  recommendations?: string[];
}

/**
 * Professional Sub-Agents Workflow Engine
 * Orchestrates the complete development workflow using professional prompts
 */
export class ProfessionalWorkflow {
  private context: ProfessionalWorkflowContext;
  private messageBus: MessageBus;
  private results: ProfessionalWorkflowResult[] = [];
  private agents: Map<AgentType, any> = new Map();

  constructor(context: ProfessionalWorkflowContext) {
    this.context = context;
    this.messageBus = new MessageBus();
  }

  /**
   * Execute complete professional workflow
   */
  async execute(): Promise<ProfessionalWorkflowResult[]> {
    console.log(`🚀 Starting Professional Sub-Agents Workflow: ${this.context.projectTitle}`);

    try {
      // Initialize agents with professional prompts
      await this.initializeAgents();

      // Execute workflow phases
      const phases: { phase: WorkflowPhase; agentType: AgentType }[] = [
        { phase: 'requirements', agentType: 'spec' },
        { phase: 'architecture', agentType: 'architect' },
        { phase: 'implementation', agentType: 'developer' },
        { phase: 'quality', agentType: 'quality' },
        { phase: 'testing', agentType: 'test' }
      ];

      for (const { phase, agentType } of phases) {
        if (this.isAgentEnabled(agentType)) {
          const result = await this.executePhase(phase, agentType);
          this.results.push(result);

          // Check quality gate
          if (!result.success) {
            console.log(`❌ Phase ${phase} failed quality gate, stopping workflow`);
            break;
          }
        }
      }

      console.log('✅ Professional workflow completed');
      return this.results;

    } catch (error) {
      console.error('❌ Professional workflow failed:', error);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * Initialize agents with professional prompts
   */
  private async initializeAgents(): Promise<void> {
    const agentContext: AgentContext = {
      projectRoot: this.context.projectRoot,
      sharedState: {},
      messageBus: this.messageBus
    };

    const enabledAgents = this.context.enabledAgents || ['spec', 'architect', 'developer', 'quality', 'test'];

    for (const agentType of enabledAgents) {
      try {
        const agent = await AgentFactory.createAgent(agentType, agentContext);
        this.agents.set(agentType, agent);
        this.messageBus.registerAgent(agent);
        await agent.start();
        console.log(`✅ Initialized ${agentType} agent with professional prompts`);
      } catch (error) {
        console.error(`❌ Failed to initialize ${agentType} agent:`, error);
      }
    }
  }

  /**
   * Execute a specific workflow phase
   */
  private async executePhase(phase: WorkflowPhase, agentType: AgentType): Promise<ProfessionalWorkflowResult> {
    const startTime = Date.now();
    console.log(`📋 Executing ${phase} phase with ${agentType} agent`);

    try {
      // Generate professional prompt for the phase
      const prompt = this.generateProfessionalPrompt(phase, agentType);
      
      // Get agent and execute task
      const agent = this.agents.get(agentType);
      if (!agent) {
        throw new Error(`Agent ${agentType} not initialized`);
      }

      // Prepare task data with professional prompt context
      const taskData = {
        title: this.context.projectTitle,
        description: this.context.projectDescription,
        phase,
        prompt,
        previousResults: this.getPreviousResults(),
        qualityThreshold: this.context.qualityThresholds[phase as keyof typeof this.context.qualityThresholds]
      };

      // Execute task
      const result = await agent.executeTask(taskData);
      const executionTime = Date.now() - startTime;

      // Calculate quality score
      const qualityScore = this.calculateQualityScore(result, phase);
      const success = qualityScore >= this.context.qualityThresholds[phase as keyof typeof this.context.qualityThresholds];

      console.log(`📊 ${phase} phase completed: ${qualityScore}% quality (threshold: ${this.context.qualityThresholds[phase as keyof typeof this.context.qualityThresholds]}%)`);

      return {
        phase,
        agentType,
        success,
        result,
        qualityScore,
        promptUsed: prompt.roleDescription,
        confidence: prompt.confidence,
        executionTime,
        recommendations: success ? [] : this.generateRecommendations(phase, qualityScore)
      };

    } catch (error) {
      console.error(`❌ Error in ${phase} phase:`, error);
      
      return {
        phase,
        agentType,
        success: false,
        result: null,
        qualityScore: 0,
        promptUsed: 'Error occurred',
        confidence: 0,
        executionTime: Date.now() - startTime,
        recommendations: [`Fix error in ${phase} phase: ${error instanceof Error ? error.message : String(error)}`]
      };
    }
  }

  /**
   * Generate professional prompt for specific phase
   */
  private generateProfessionalPrompt(phase: WorkflowPhase, agentType: AgentType): any {
    const previousResults = this.getPreviousResults();

    switch (phase) {
      case 'requirements':
        return ProfessionalPromptFactory.forRequirementsAnalysis(
          this.context.projectDescription,
          this.context.projectTitle,
          this.context.qualityThresholds.requirements
        );

      case 'architecture':
        return ProfessionalPromptFactory.forArchitectureDesign(
          previousResults.requirements,
          'medium', // TODO: detect scale from description
          'Node.js + React', // TODO: detect from requirements
          this.context.qualityThresholds.architecture
        );

      case 'implementation':
        return ProfessionalPromptFactory.forFeatureImplementation(
          previousResults.architecture,
          previousResults.requirements,
          'TypeScript + Jest',
          this.context.qualityThresholds.implementation
        );

      case 'quality':
        return ProfessionalPromptFactory.forQualityAnalysis(
          JSON.stringify(previousResults.implementation),
          previousResults.architecture,
          { coverage: 85 },
          this.context.qualityThresholds.quality
        );

      case 'testing':
        return ProfessionalPromptFactory.forTestStrategy(
          previousResults.requirements,
          previousResults.architecture,
          { coverage: 90 },
          this.context.qualityThresholds.testing
        );

      default:
        throw new Error(`Unknown phase: ${phase}`);
    }
  }

  /**
   * Get previous phase results
   */
  private getPreviousResults(): any {
    const results: any = {};
    
    for (const result of this.results) {
      if (result.success) {
        results[result.phase] = result.result;
      }
    }
    
    return results;
  }

  /**
   * Calculate quality score for phase result
   */
  private calculateQualityScore(result: any, phase: WorkflowPhase): number {
    // Basic quality scoring - can be enhanced based on specific criteria
    if (!result) return 0;
    
    if (result.qualityScore !== undefined) {
      return result.qualityScore;
    }

    // Default scoring based on result completeness
    let score = 70; // Base score
    
    if (result.data && typeof result.data === 'object') {
      const keys = Object.keys(result.data);
      score += Math.min(keys.length * 5, 20);
    }
    
    return Math.min(score, 100);
  }

  /**
   * Generate recommendations for failed phases
   */
  private generateRecommendations(phase: WorkflowPhase, qualityScore: number): string[] {
    const recommendations: string[] = [];
    
    if (qualityScore < 50) {
      recommendations.push(`${phase} phase needs significant improvement`);
    } else if (qualityScore < 70) {
      recommendations.push(`${phase} phase needs moderate improvement`);
    } else {
      recommendations.push(`${phase} phase needs minor adjustments`);
    }
    
    return recommendations;
  }

  /**
   * Check if agent is enabled
   */
  private isAgentEnabled(agentType: AgentType): boolean {
    const enabledAgents = this.context.enabledAgents || ['spec', 'architect', 'developer', 'quality', 'test'];
    return enabledAgents.includes(agentType);
  }

  /**
   * Cleanup resources
   */
  private async cleanup(): Promise<void> {
    for (const [agentType, agent] of this.agents) {
      try {
        await agent.stop();
        console.log(`🛑 Stopped ${agentType} agent`);
      } catch (error) {
        console.error(`❌ Error stopping ${agentType} agent:`, error);
      }
    }
    
    this.agents.clear();
  }

  /**
   * Get workflow summary
   */
  getWorkflowSummary(): any {
    const totalPhases = this.results.length;
    const successfulPhases = this.results.filter(r => r.success).length;
    const averageQuality = totalPhases > 0 
      ? this.results.reduce((sum, r) => sum + r.qualityScore, 0) / totalPhases 
      : 0;
    const totalExecutionTime = this.results.reduce((sum, r) => sum + r.executionTime, 0);

    return {
      projectTitle: this.context.projectTitle,
      totalPhases,
      successfulPhases,
      successRate: totalPhases > 0 ? (successfulPhases / totalPhases) * 100 : 0,
      averageQuality,
      totalExecutionTime,
      results: this.results
    };
  }
}
