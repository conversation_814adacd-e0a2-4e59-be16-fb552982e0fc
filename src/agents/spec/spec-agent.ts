/**
 * Spec Agent - 规格代理
 * v1.4.0 - Sub-Agents 革命
 */

import { BaseAgent } from '../base-agent.js';
import {
  AgentMessage,
  AgentContext,
  RequirementsState,
  UserStory,
  AcceptanceCriteria
} from '../types.js';
import { AIRequirementsAnalysisEngine } from '../engines/ai-analysis-engine.js';

export class SpecAgent extends BaseAgent {
  constructor(context: AgentContext) {
    super('spec', context);
  }

  protected async onStart(): Promise<void> {
    console.log('📋 Spec Agent ready to analyze requirements');
  }

  protected async onStop(): Promise<void> {
    console.log('📋 Spec Agent stopped');
  }

  protected async processMessage(message: AgentMessage): Promise<AgentMessage | null> {
    switch (message.type) {
      case 'request':
        return await this.handleRequest(message);
      default:
        return null;
    }
  }

  private async handleRequest(message: AgentMessage): Promise<AgentMessage | null> {
    const { action, data } = message.payload;

    switch (action) {
      case 'generate-requirements':
        return await this.generateRequirements(data, message);
      default:
        return {
          id: `error-${Date.now()}`,
          type: 'error',
          to: message.from,
          payload: { error: `Unknown action: ${action}` },
          timestamp: new Date().toISOString(),
          correlationId: message.correlationId
        };
    }
  }

  private async generateRequirements(data: any, originalMessage: AgentMessage): Promise<AgentMessage> {
    this.setCurrentTask('Analyzing and generating requirements');

    try {
      console.log(`📋 Generating requirements for: ${data.title}`);

      // 使用 AI 驱动的智能分析引擎
      const analysisEngine = new AIRequirementsAnalysisEngine({
        projectRoot: this.context.projectRoot,
        description: data.description,
        title: data.title
      });

      const analysisResult = await analysisEngine.analyze();
      const qualityScore = analysisResult.quality;

      const requirementsState: RequirementsState = {
        specName: analysisResult.data.specName,
        title: data.title,
        description: data.description,
        userStories: analysisResult.data.userStories,
        acceptanceCriteria: analysisResult.data.acceptanceCriteria,
        qualityScore
      };

      // 更新共享状态
      this.updateSharedState({ requirements: requirementsState });

      // 检查质量门控
      const qualityPassed = await this.checkQualityGates('requirements', qualityScore);

      console.log(`📋 Requirements generated with quality score: ${qualityScore}% (confidence: ${analysisResult.confidence}%)`);

      return {
        id: `req-response-${Date.now()}`,
        type: 'response',
        from: this.agentId,
        to: originalMessage.from,
        payload: {
          phase: 'requirements',
          success: qualityPassed,
          result: requirementsState,
          qualityScore,
          confidence: analysisResult.confidence,
          metadata: analysisResult.metadata,
          message: qualityPassed ? 'Requirements generated successfully' : 'Requirements quality below threshold'
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };

    } catch (error) {
      console.error('❌ Error generating requirements:', error);

      return {
        id: `req-error-${Date.now()}`,
        type: 'response',
        from: this.agentId,
        to: originalMessage.from,
        payload: {
          phase: 'requirements',
          success: false,
          error: error instanceof Error ? error.message : String(error),
          qualityScore: 0
        },
        timestamp: new Date().toISOString(),
        correlationId: originalMessage.correlationId
      };
    }
  }

  // 所有硬编码的分析方法已被 RequirementsAnalysisEngine 替代
  // 这提供了更智能、可配置的需求分析能力

  async executeTask(taskData: any): Promise<any> {
    return await this.generateRequirements(taskData, {
      id: 'direct-task',
      type: 'request',
      from: 'user',
      to: this.agentId,
      payload: { action: 'generate-requirements', data: taskData },
      timestamp: new Date().toISOString()
    });
  }

  getCapabilities(): string[] {
    return [
      'Requirements analysis and specification',
      'User story generation (EARS format)',
      'Acceptance criteria definition',
      'Requirements quality assessment',
      'Stakeholder needs analysis',
      'Functional and non-functional requirements'
    ];
  }
}
