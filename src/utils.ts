/**
 * Utility functions for Vibe Coding workflow system
 */

import { existsSync, promises as fs } from "fs";
import * as path from "path";
import { WorkflowConfig, SubAgentsConfig } from "./types.js";

/**
 * Ensure workflow directories exist
 */
export async function ensureWorkflowDirectories(rootPath: string): Promise<void> {
  const workflowDirs = [
    path.join(rootPath, ".vibecode", "steering"),
    path.join(rootPath, ".vibecode", "workflows", "specs"),
    path.join(rootPath, ".vibecode", "workflows", "bugs"),
    path.join(rootPath, ".vibecode", "templates")
  ];

  for (const dir of workflowDirs) {
    await fs.mkdir(dir, { recursive: true });
  }
}

/**
 * Load workflow configuration
 */
export async function loadWorkflowConfig(rootPath: string): Promise<WorkflowConfig> {
  // 优先尝试新的简化配置位置
  const newConfigPath = path.join(rootPath, ".vibecode", "config.json");
  const oldConfigPath = path.join(rootPath, ".vibecode", "workflows", "config.json");

  let configPath = newConfigPath;
  if (!existsSync(newConfigPath) && existsSync(oldConfigPath)) {
    configPath = oldConfigPath;
    console.log('⚠️  Using legacy config location. Consider moving to .vibecode/config.json');
  }

  try {
    const content = await fs.readFile(configPath, "utf-8");
    return JSON.parse(content);
  } catch {
    // If config file doesn't exist, return default config
    const defaultConfig: WorkflowConfig = {
      specs: {},
      bugs: {},
      version: "1.0.0"
    };
    await saveWorkflowConfig(rootPath, defaultConfig);
    return defaultConfig;
  }
}

/**
 * Save workflow configuration
 */
export async function saveWorkflowConfig(rootPath: string, config: WorkflowConfig): Promise<void> {
  const configPath = path.join(rootPath, ".vibecode", "config.json");
  await fs.writeFile(configPath, JSON.stringify(config, null, 2), "utf-8");
}

/**
 * Generate spec name from title
 */
export function generateSpecName(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim();
}

/**
 * Get current timestamp
 */
export function getCurrentTimestamp(): string {
  return new Date().toISOString();
}

/**
 * Load Sub-Agents configuration from .vibecode/config.json
 */
export async function loadSubAgentsConfig(rootPath: string): Promise<SubAgentsConfig> {
  const config = await loadWorkflowConfig(rootPath);

  if (config.subAgents) {
    return {
      qualityThreshold: config.subAgents.qualityThreshold || 95,
      outputFormat: config.subAgents.outputFormat || "detailed",
      autoRestart: config.subAgents.autoRestart !== false,
      maxRetries: config.subAgents.maxRetries || 3,
      maxWorkflowRetries: config.subAgents.maxWorkflowRetries || 2,
      enabledAgents: config.subAgents.enabledAgents || ["spec", "architect", "developer", "quality", "test"],
      phaseThresholds: config.subAgents.phaseThresholds || {
        implementation: 85,
        quality: 75,
        testing: 70
      },
      saveResults: config.subAgents.saveResults !== false,
      showProgress: config.subAgents.showProgress !== false
    };
  }

  // Return default Sub-Agents configuration if not found
  const defaultSubAgentsConfig: SubAgentsConfig = {
    qualityThreshold: 95,
    outputFormat: "detailed",
    autoRestart: true,
    maxRetries: 3,
    maxWorkflowRetries: 2,
    enabledAgents: ["spec", "architect", "developer", "quality", "test"],
    phaseThresholds: {
      implementation: 85,
      quality: 75,
      testing: 70
    },
    saveResults: true,
    showProgress: true
  };

  return defaultSubAgentsConfig;
}

/**
 * Parse .gitignore file
 */
export async function parseGitignore(
  rootPath: string,
  targetPath: string
): Promise<boolean | null> {
  const gitignorePath = path.join(rootPath, ".gitignore");

  // Check if .gitignore file exists
  if (!existsSync(gitignorePath)) {
    return null;
  }

  try {
    // Read .gitignore file content
    const content = await fs.readFile(gitignorePath, "utf-8");
    // Use gitignore-parser's compile method to parse .gitignore content
    const gitignoreParser = await import("gitignore-parser");
    const gitignore = gitignoreParser.compile(content);

    // Use denies method to check if path is rejected (ignored)
    return gitignore.denies(targetPath);
  } catch (error) {
    console.error("Error parsing .gitignore:", error);
    return null;
  }
}
