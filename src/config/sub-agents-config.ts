/**
 * Sub-Agents 统一配置管理系统
 * v1.4.0 - Sub-Agents 革命
 */

import { readFileSync, writeFileSync, existsSync } from 'fs';
import { join } from 'path';
import { z } from 'zod';

// 配置模式定义
const AgentConfigSchema = z.object({
  enabled: z.boolean().default(true),
  timeout: z.number().min(1000).default(30000),
  maxRetries: z.number().min(0).default(3),
  qualityThreshold: z.number().min(0).max(100).default(85),
  capabilities: z.array(z.string()).default([]),
  integrations: z.record(z.any()).default({})
});

const WorkflowConfigSchema = z.object({
  timeout: z.number().min(10000).default(300000),
  maxRetries: z.number().min(0).default(2),
  qualityThreshold: z.number().min(0).max(100).default(95),
  phases: z.array(z.string()).default([
    'initialization', 'requirements', 'architecture', 
    'implementation', 'quality', 'testing', 'integration', 'completion'
  ]),
  phaseThresholds: z.record(z.number()).default({
    requirements: 90,
    architecture: 85,
    implementation: 85,
    quality: 75,
    testing: 70,
    integration: 85
  }),
  parallelExecution: z.boolean().default(false),
  saveResults: z.boolean().default(true),
  showProgress: z.boolean().default(true)
});

const SubAgentsConfigSchema = z.object({
  version: z.string().default('1.4.0'),
  workflow: WorkflowConfigSchema,
  agents: z.object({
    orchestrator: AgentConfigSchema,
    spec: AgentConfigSchema,
    architect: AgentConfigSchema,
    developer: AgentConfigSchema,
    quality: AgentConfigSchema,
    test: AgentConfigSchema
  }),
  integrations: z.object({
    ai: z.object({
      provider: z.enum(['openai', 'anthropic', 'local']).default('openai'),
      model: z.string().default('gpt-4'),
      apiKey: z.string().optional(),
      baseUrl: z.string().optional(),
      timeout: z.number().default(30000)
    }).default({}),
    codeAnalysis: z.object({
      enabled: z.boolean().default(true),
      tools: z.array(z.string()).default(['eslint', 'prettier', 'typescript'])
    }).default({}),
    testing: z.object({
      frameworks: z.array(z.string()).default(['jest', 'vitest', 'cypress']),
      coverage: z.object({
        threshold: z.number().min(0).max(100).default(80),
        enabled: z.boolean().default(true)
      }).default({})
    }).default({})
  }).default({})
});

export type SubAgentsConfig = z.infer<typeof SubAgentsConfigSchema>;
export type AgentConfig = z.infer<typeof AgentConfigSchema>;
export type WorkflowConfig = z.infer<typeof WorkflowConfigSchema>;

/**
 * Sub-Agents 配置管理器
 */
export class SubAgentsConfigManager {
  private static instance: SubAgentsConfigManager;
  private config: SubAgentsConfig;
  private configPath: string;

  private constructor(rootPath: string) {
    this.configPath = join(rootPath, '.vibecode', 'sub-agents-config.json');
    this.config = this.loadConfig();
  }

  /**
   * 获取配置管理器实例
   */
  static getInstance(rootPath: string): SubAgentsConfigManager {
    if (!SubAgentsConfigManager.instance) {
      SubAgentsConfigManager.instance = new SubAgentsConfigManager(rootPath);
    }
    return SubAgentsConfigManager.instance;
  }

  /**
   * 加载配置
   */
  private loadConfig(): SubAgentsConfig {
    try {
      if (existsSync(this.configPath)) {
        const configData = JSON.parse(readFileSync(this.configPath, 'utf-8'));
        return SubAgentsConfigSchema.parse(configData);
      }
    } catch (error) {
      console.warn('⚠️ Failed to load Sub-Agents config, using defaults:', error);
    }

    // 返回默认配置
    const defaultConfig = this.getDefaultConfig();
    this.saveConfig(defaultConfig);
    return defaultConfig;
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): SubAgentsConfig {
    return {
      version: '1.4.0',
      workflow: {
        timeout: 300000,
        maxRetries: 2,
        qualityThreshold: 95,
        phases: [
          'initialization', 'requirements', 'architecture',
          'implementation', 'quality', 'testing', 'integration', 'completion'
        ],
        phaseThresholds: {
          requirements: 90,
          architecture: 85,
          implementation: 85,
          quality: 75,
          testing: 70,
          integration: 85
        },
        parallelExecution: false,
        saveResults: true,
        showProgress: true
      },
      agents: {
        orchestrator: {
          enabled: true,
          timeout: 60000,
          maxRetries: 3,
          qualityThreshold: 95,
          capabilities: [
            'workflow-orchestration',
            'phase-management',
            'quality-gate-enforcement',
            'agent-coordination'
          ],
          integrations: {}
        },
        spec: {
          enabled: true,
          timeout: 45000,
          maxRetries: 3,
          qualityThreshold: 90,
          capabilities: [
            'requirements-analysis',
            'user-story-generation',
            'acceptance-criteria-definition',
            'requirements-quality-assessment'
          ],
          integrations: {}
        },
        architect: {
          enabled: true,
          timeout: 60000,
          maxRetries: 3,
          qualityThreshold: 85,
          capabilities: [
            'system-architecture-design',
            'technical-stack-selection',
            'api-design',
            'data-modeling'
          ],
          integrations: {}
        },
        developer: {
          enabled: true,
          timeout: 120000,
          maxRetries: 3,
          qualityThreshold: 85,
          capabilities: [
            'intelligent-code-generation',
            'code-optimization',
            'best-practices-implementation',
            'code-quality-assurance'
          ],
          integrations: {}
        },
        quality: {
          enabled: true,
          timeout: 60000,
          maxRetries: 3,
          qualityThreshold: 75,
          capabilities: [
            'code-quality-analysis',
            'security-vulnerability-scanning',
            'performance-assessment',
            'technical-debt-measurement'
          ],
          integrations: {}
        },
        test: {
          enabled: true,
          timeout: 90000,
          maxRetries: 3,
          qualityThreshold: 70,
          capabilities: [
            'automated-test-generation',
            'test-coverage-analysis',
            'test-quality-assessment',
            'multiple-frameworks-support'
          ],
          integrations: {}
        }
      },
      integrations: {
        ai: {
          provider: 'openai',
          model: 'gpt-4',
          timeout: 30000
        },
        codeAnalysis: {
          enabled: true,
          tools: ['eslint', 'prettier', 'typescript']
        },
        testing: {
          frameworks: ['jest', 'vitest', 'cypress'],
          coverage: {
            threshold: 80,
            enabled: true
          }
        }
      }
    };
  }

  /**
   * 保存配置
   */
  private saveConfig(config: SubAgentsConfig): void {
    try {
      writeFileSync(this.configPath, JSON.stringify(config, null, 2), 'utf-8');
    } catch (error) {
      console.error('❌ Failed to save Sub-Agents config:', error);
    }
  }

  /**
   * 获取完整配置
   */
  getConfig(): SubAgentsConfig {
    return this.config;
  }

  /**
   * 获取工作流程配置
   */
  getWorkflowConfig(): WorkflowConfig {
    return this.config.workflow;
  }

  /**
   * 获取代理配置
   */
  getAgentConfig(agentType: string): AgentConfig | undefined {
    return (this.config.agents as any)[agentType];
  }

  /**
   * 更新配置
   */
  updateConfig(updates: Partial<SubAgentsConfig>): void {
    this.config = SubAgentsConfigSchema.parse({ ...this.config, ...updates });
    this.saveConfig(this.config);
  }

  /**
   * 重新加载配置
   */
  reloadConfig(): void {
    this.config = this.loadConfig();
  }

  /**
   * 验证配置
   */
  validateConfig(): { valid: boolean; errors: string[] } {
    try {
      SubAgentsConfigSchema.parse(this.config);
      return { valid: true, errors: [] };
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          valid: false,
          errors: error.errors.map(e => `${e.path.join('.')}: ${e.message}`)
        };
      }
      return { valid: false, errors: [String(error)] };
    }
  }
}
