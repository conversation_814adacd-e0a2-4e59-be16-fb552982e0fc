# 🤖 MCP 驱动的 Sub-Agents 系统

## 🎯 概述

基于 MCP (Model Context Protocol) 设计理念的 Sub-Agents 系统，完全消除了硬编码问题。由调用的 AI 直接提供智能分析，无需额外的 AI API 集成。

## ✨ 核心特性

### 🚀 MCP 设计理念
- **无需外部 AI API**: 直接使用调用工具的 AI 进行分析
- **简化架构**: 移除复杂的 AI 提供商集成层
- **更高效率**: 减少网络调用和 API 依赖
- **一致性保证**: 同一个 AI 进行所有分析

### 🔧 配置驱动
- **统一配置管理**: 所有设置都可通过配置文件调整
- **简化配置**: 无需配置 AI API 密钥
- **质量门控**: 可配置的质量阈值和评估标准

### 🎨 MCP 分析引擎
- **需求分析引擎**: MCP 驱动的用户故事和验收标准生成
- **架构分析引擎**: 智能技术栈选择和系统设计
- **代码分析引擎**: 智能代码生成和质量评估
- **测试分析引擎**: 自动测试用例生成和覆盖率分析
- **质量分析引擎**: 代码质量和安全性评估

## 🛠️ 配置指南

### 1. 基础配置

复制示例配置文件：
```bash
cp .vibecode/sub-agents-config.example.json .vibecode/sub-agents-config.json
```

### 2. 配置结构

```json
{
  "version": "1.4.0",
  "workflow": {
    "timeout": 300000,
    "qualityThreshold": 95,
    "phases": ["initialization", "requirements", "architecture", "implementation", "quality", "testing", "completion"]
  },
  "agents": {
    "spec": { "enabled": true, "qualityThreshold": 90 },
    "architect": { "enabled": true, "qualityThreshold": 85 },
    "developer": { "enabled": true, "qualityThreshold": 85 },
    "quality": { "enabled": true, "qualityThreshold": 75 },
    "test": { "enabled": true, "qualityThreshold": 70 }
  }
}
```

## 🚀 使用方法

### 基础用法
```bash
# 直接使用，无需配置 AI API
/vibe-coding "开发用户认证系统"

# 指定质量阈值
/vibe-coding "开发电商平台" --qualityThreshold 90

# 启用特定代理
/vibe-coding "开发 CMS 系统" --enabledAgents spec,architect,developer
```

### 高级用法
```bash
# 查看配置状态
/vibe-status

# 查看代理能力
/vibe-agents
```

## 🎨 MCP 分析引擎架构

### 核心设计

```typescript
export abstract class MCPAnalysisEngine {
  // 由调用的 AI 提供具体分析逻辑
  protected abstract performAnalysis(): Promise<any>;
  
  // 自动质量评估
  protected abstract calculateQuality(data: any): Promise<number>;
  
  // 智能推理生成
  protected abstract generateReasoning(data: any): string;
}
```

### 需求分析引擎

```typescript
export class MCPRequirementsAnalysisEngine extends MCPAnalysisEngine {
  protected async performAnalysis(): Promise<any> {
    const domain = this.detectDomain(this.context.description);
    const technology = this.detectTechnology(this.context.description);
    const complexity = this.assessComplexity(this.context.description);
    
    return {
      domain,
      technology,
      complexity,
      userStories: this.generateUserStories(domain, complexity),
      acceptanceCriteria: this.generateAcceptanceCriteria(),
      specName: this.generateSpecName(this.context.title)
    };
  }
}
```

## 🔍 智能分析能力

### 自动领域识别
- **认证系统**: auth, login, register, user
- **电商平台**: shop, order, cart, payment
- **内容管理**: content, article, blog, cms
- **数据分析**: dashboard, admin, analytics

### 技术栈智能推荐
- **React 生态**: react, next.js, typescript
- **Vue 生态**: vue, nuxt, vuejs
- **后端技术**: node, express, python, java
- **数据库**: postgresql, mongodb, redis

### 复杂度自动评估
- **简单**: 基础 CRUD 操作
- **中等**: 标准业务逻辑
- **复杂**: 企业级、微服务架构

## 🛡️ 质量保证

### 多层质量检查
1. **分析质量**: 评估生成内容的完整性和准确性
2. **内容验证**: 检查生成内容是否符合要求
3. **一致性检查**: 确保不同阶段输出的一致性
4. **最佳实践**: 验证是否遵循行业最佳实践

### 质量门控
```json
{
  "workflow": {
    "phaseThresholds": {
      "requirements": 90,
      "architecture": 85,
      "implementation": 85,
      "quality": 75,
      "testing": 70
    }
  }
}
```

## 🌟 MCP 设计优势

1. **简化部署**: 无需配置外部 AI 服务
2. **降低成本**: 不产生额外的 AI API 费用
3. **提高可靠性**: 减少外部依赖和网络故障
4. **保证一致性**: 同一个 AI 进行所有分析
5. **更好的上下文**: AI 可以利用完整的对话上下文
6. **即开即用**: 无需复杂的配置过程

## 📁 项目结构

```
src/
├── config/
│   └── sub-agents-config.ts              # 配置管理
├── agents/
│   ├── engines/
│   │   └── mcp-analysis-engine.ts         # MCP 分析引擎
│   ├── spec/spec-agent.ts                 # 需求分析代理
│   ├── architect/architect-agent.ts       # 架构设计代理
│   ├── developer/developer-agent.ts       # 代码实现代理
│   ├── quality/quality-agent.ts           # 质量分析代理
│   └── test/test-agent.ts                 # 测试生成代理
└── tools/
    └── sub-agents.ts                      # 主工具入口
```

## 🎯 最佳实践

1. **明确的项目描述**: 提供详细、清晰的项目描述
2. **合适的质量阈值**: 根据项目重要性设置质量要求
3. **定期更新配置**: 保持配置文件与项目需求同步
4. **监控分析质量**: 关注分析结果的质量和一致性

---

通过 MCP 驱动的设计，Sub-Agents 系统现在具备了真正的智能分析能力，同时保持了简洁性和可靠性。
